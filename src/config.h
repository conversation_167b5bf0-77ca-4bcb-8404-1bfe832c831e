#ifndef CONFIG_H
#define CONFIG_H

// WiFi AP配置 (配置模式)
#define AP_SSID "VFD_Clock_AP"
#define AP_PASSWORD "12345678"

// NTP服务器配置
#define NTP_SERVER1 "ntp.aliyun.com"
#define NTP_SERVER2 "pool.ntp.org"
#define NTP_SERVER3 "time.nist.gov"

// 时区配置 (默认东八区)
#define DEFAULT_TIMEZONE 8

// 显示配置
#define DISPLAY_BRIGHTNESS 75
#define TIME_UPDATE_INTERVAL 1000    // 1秒更新一次时间
#define CONFIG_TIMEOUT 300000        // 5分钟配置超时

// 显示模式
enum DisplayMode {
    MODE_FULL,      // HH:MM:SS MM/DD
    MODE_TIME_ONLY  // HH:MM:SS
};

// 字体样式定义已移至VFD_Driver.h

#endif
