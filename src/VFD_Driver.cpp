#include "VFD_Driver.h"
#include "font_5x7_digits.h"

VFD_Driver::VFD_Driver() {
    brightness = 75;
    isInitialized = false;
    for (int i = 0; i < 10; i++) digitToCGRAM[i] = -1;
    for (int i = 0; i < 8; i++) cgramContent[i] = -1;
    lastText = "";
}

bool VFD_Driver::init() {
    // 初始化引脚 - 基于原始ESP32C3.ino代码
    pinMode(VFD_CLK_PIN, OUTPUT);
    pinMode(VFD_DATA_PIN, OUTPUT);
    pinMode(VFD_CS_PIN, OUTPUT);

    // 初始状态
    digitalWrite(VFD_CLK_PIN, LOW);
    digitalWrite(VFD_DATA_PIN, LOW);
    digitalWrite(VFD_CS_PIN, HIGH);

    delay(100);

    // PT6302初始化序列 - 基于原始工作代码
    chipSelect(true);
    sendByte(0x02);  // 显示模式设置
    sendByte(0x0C);  // 显示开启
    chipSelect(false);

    // 设置默认亮度
    setBrightness(75);

    // 清屏
    clear();

    isInitialized = true;
    return true;
}

bool VFD_Driver::begin() {
    return init();  // 兼容性别名
}

void VFD_Driver::delay1() {
    delayMicroseconds(1);
}

void VFD_Driver::sendByte(uint8_t data) {
    // PT6302使用LSB优先传输
    for (int i = 0; i < 8; i++) {
        digitalWrite(VFD_DATA_PIN, (data >> i) & 0x01);
        delayMicroseconds(1);
        digitalWrite(VFD_CLK_PIN, HIGH);
        delayMicroseconds(1);
        digitalWrite(VFD_CLK_PIN, LOW);
        delayMicroseconds(1);
    }
}

void VFD_Driver::chipSelect(bool select) {
    digitalWrite(VFD_CS_PIN, select ? LOW : HIGH);
    delayMicroseconds(1);
}

void VFD_Driver::clear() {
    chipSelect(true);
    sendByte(0x20);  // 设置DDRAM地址为0
    for (int i = 0; i < VFD_DIGITS; i++) {
        sendByte(0x20);  // 发送空格字符
    }
    chipSelect(false);
    showDisplay();
}

void VFD_Driver::showDisplay() {
    chipSelect(true);
    sendByte(0xe8);  // 显示刷新命令
    chipSelect(false);
}

void VFD_Driver::setBrightness(uint8_t level) {
    brightness = constrain(level, 0, 100);
    // PT6302亮度控制：0x00-0xFF
    uint8_t pt6302Level = map(brightness, 0, 100, 0, 255);

    chipSelect(true);
    sendByte(0xe4);  // 亮度设置命令
    sendByte(pt6302Level);
    chipSelect(false);
}

void VFD_Driver::writeOneChar(uint8_t position, char character) {
    chipSelect(true);
    sendByte(0x20 + position);
    sendByte(character);
    chipSelect(false);
}

void VFD_Driver::writeString(uint8_t position, const char* str) {
    chipSelect(true);
    sendByte(0x20 + position);
    while (*str && position < VFD_DIGITS) {
        sendByte(*str);
        str++;
        position++;
    }
    chipSelect(false);
    showDisplay();
}

void VFD_Driver::displayText(const char* text) {
    char displayBuffer[17];
    memset(displayBuffer, ' ', 16);
    displayBuffer[16] = '\0';
    
    size_t textLen = strlen(text);
    if (textLen > 16) textLen = 16;
    memcpy(displayBuffer, text, textLen);
    
    chipSelect(true);
    sendByte(0x20);
    for (int i = 0; i < 16; i++) {
        sendByte(displayBuffer[i]);
    }
    chipSelect(false);
    showDisplay();
}

void VFD_Driver::writeCGRAM(uint8_t address, const uint8_t* fontData) {
    if (address > 7) return;

    Serial.println("🔤 Writing CGRAM[" + String(address) + "] with correct method");

    chipSelect(true);
    sendByte(0x40 + address);  // 修正：不乘以8

    // 关键修复：按列重新组织5x7点阵数据（基于日本开发者的正确方法）
    for (int col = 4; col >= 0; col--) {  // 5列，从右到左
        uint8_t bitpos = 0x01 << col;
        uint8_t submit = 0;

        // 将7行的第col列数据组合成一个字节
        submit |= ((bitpos & fontData[0]) ? 1 : 0) << 0;  // 第0行
        submit |= ((bitpos & fontData[1]) ? 1 : 0) << 1;  // 第1行
        submit |= ((bitpos & fontData[2]) ? 1 : 0) << 2;  // 第2行
        submit |= ((bitpos & fontData[3]) ? 1 : 0) << 3;  // 第3行
        submit |= ((bitpos & fontData[4]) ? 1 : 0) << 4;  // 第4行
        submit |= ((bitpos & fontData[5]) ? 1 : 0) << 5;  // 第5行
        submit |= ((bitpos & fontData[6]) ? 1 : 0) << 6;  // 第6行

        sendByte(submit);
        delayMicroseconds(10);  // 增加CGRAM写入延迟，确保数据稳定
    }

    chipSelect(false);
    Serial.println("✅ CGRAM written with correct column-based method");
}

void VFD_Driver::loadDynamicFont(const char* text) {
    bool needDigits[10] = {false};
    for (int i = 0; text[i] != '\0'; i++) {
        if (text[i] >= '0' && text[i] <= '9') {
            needDigits[text[i] - '0'] = true;
        }
    }
    
    int cgramPos = 0;
    for (int digit = 0; digit < 10 && cgramPos < 8; digit++) {
        if (needDigits[digit]) {
            writeCGRAM(cgramPos, digitFont_Square[digit]);
            digitToCGRAM[digit] = cgramPos;
            cgramContent[cgramPos] = digit;
            cgramPos++;
        }
    }
}

void VFD_Driver::loadDynamicFontBold(const char* text) {
    bool needDigits[10] = {false};
    for (int i = 0; text[i] != '\0'; i++) {
        if (text[i] >= '0' && text[i] <= '9') {
            needDigits[text[i] - '0'] = true;
        }
    }
    
    int cgramPos = 0;
    for (int digit = 0; digit < 10 && cgramPos < 8; digit++) {
        if (needDigits[digit]) {
            writeCGRAM(cgramPos, digitFont_Bold[digit]);
            digitToCGRAM[digit] = cgramPos;
            cgramContent[cgramPos] = digit;
            cgramPos++;
        }
    }
}

void VFD_Driver::displayTextWithCGRAM(const char* text) {
    chipSelect(true);
    sendByte(0x20);
    
    for (int i = 0; i < strlen(text) && i < 16; i++) {
        char c = text[i];
        uint8_t charCode;
        
        if (c >= '0' && c <= '9') {
            int digit = c - '0';
            int cgramPos = digitToCGRAM[digit];
            if (cgramPos >= 0) {
                charCode = cgramPos;
            } else {
                charCode = 0x30 + digit;
            }
        } else {
            charCode = (uint8_t)c;
        }
        
        sendByte(charCode);
    }
    
    chipSelect(false);
    showDisplay();
}

void VFD_Driver::displayTimeWithFontType(const char* timeStr, int fontType) {
    switch(fontType) {
        case 0:
            displayText(timeStr);
            break;
        case 1:
            loadDynamicFont(timeStr);
            displayTextWithCGRAM(timeStr);
            break;
        case 2:
            loadDynamicFontBold(timeStr);
            displayTextWithCGRAM(timeStr);
            break;
        default:
            displayText(timeStr);
            break;
    }
}

// 重置显示缓存 - 用于字体切换时清空缓存
void VFD_Driver::resetDisplayBuffer() {
    memset(displayBuffer, ' ', 16);
    displayBuffer[16] = '\0';
    Serial.println("🔄 Display buffer reset");
}
