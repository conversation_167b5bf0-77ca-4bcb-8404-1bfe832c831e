#ifndef FONT5X7_H
#define FONT5X7_H

#include <Arduino.h>

// 简化的5x7点阵字体定义 - 确保兼容性
// 普通字体 - 数字0-9 (7行数据，每行5位)
const uint8_t PROGMEM font5x7_digits_normal[10][7] = {
    // 0 - 简化版本
    {
        0b01110,  // .###.
        0b10001,  // #...#
        0b10001,  // #...#
        0b10001,  // #...#
        0b10001,  // #...#
        0b10001,  // #...#
        0b01110   // .###.
    },
    // 1 - 简化版本
    {
        0b00100,  // ..#..
        0b01100,  // .##..
        0b00100,  // ..#..
        0b00100,  // ..#..
        0b00100,  // ..#..
        0b00100,  // ..#..
        0b01110   // .###.
    },
    // 2 - 简化版本
    {
        0b01110,  // .###.
        0b10001,  // #...#
        0b00001,  // ....#
        0b00010,  // ...#.
        0b00100,  // ..#..
        0b01000,  // .#...
        0b11111   // #####
    },
    // 3 - 简化版本
    {
        0b01110,  // .###.
        0b10001,  // #...#
        0b00001,  // ....#
        0b00110,  // ..##.
        0b00001,  // ....#
        0b10001,  // #...#
        0b01110   // .###.
    },
    // 4 - 简化版本
    {
        0b00010,  // ...#.
        0b00110,  // ..##.
        0b01010,  // .#.#.
        0b10010,  // #..#.
        0b11111,  // #####
        0b00010,  // ...#.
        0b00010   // ...#.
    },
    // 5 - 简化版本
    {
        0b11111,  // #####
        0b10000,  // #....
        0b11110,  // ####.
        0b00001,  // ....#
        0b00001,  // ....#
        0b10001,  // #...#
        0b01110   // .###.
    },
    // 6 - 简化版本
    {
        0b00110,  // ..##.
        0b01000,  // .#...
        0b10000,  // #....
        0b11110,  // ####.
        0b10001,  // #...#
        0b10001,  // #...#
        0b01110   // .###.
    },
    // 7 - 简化版本
    {
        0b11111,  // #####
        0b00001,  // ....#
        0b00010,  // ...#.
        0b00100,  // ..#..
        0b01000,  // .#...
        0b01000,  // .#...
        0b01000   // .#...
    },
    // 8 - 简化版本
    {
        0b01110,  // .###.
        0b10001,  // #...#
        0b10001,  // #...#
        0b01110,  // .###.
        0b10001,  // #...#
        0b10001,  // #...#
        0b01110   // .###.
    },
    // 9 - 简化版本
    {
        0b01110,  // .###.
        0b10001,  // #...#
        0b10001,  // #...#
        0b01111,  // .####
        0b00001,  // ....#
        0b00010,  // ...#.
        0b01100   // .##..
    }
};

// 粗体字体 - 数字0-9 (在普通字体基础上向右扩展一列)
const uint8_t PROGMEM font5x7_digits_bold[10][7] = {
    // 0 - 粗体 (01110 -> 01111)
    {
        0b01111,  // .####
        0b11011,  // ##.##
        0b11011,  // ##.##
        0b11011,  // ##.##
        0b11011,  // ##.##
        0b11011,  // ##.##
        0b01111   // .####
    },
    // 1 - 粗体 (00100 -> 00110)
    {
        0b00110,  // ..##.
        0b01110,  // .###.
        0b00110,  // ..##.
        0b00110,  // ..##.
        0b00110,  // ..##.
        0b00110,  // ..##.
        0b01111   // .####
    },
    // 2 - 粗体 (01110 -> 01111)
    {
        0b01111,  // .####
        0b11011,  // ##.##
        0b00011,  // ...##
        0b00110,  // ..##.
        0b01100,  // .##..
        0b11000,  // ##...
        0b11111   // #####
    },
    // 3 - 粗体 (01110 -> 01111)
    {
        0b01111,  // .####
        0b11011,  // ##.##
        0b00011,  // ...##
        0b00111,  // ..###
        0b00011,  // ...##
        0b11011,  // ##.##
        0b01111   // .####
    },
    // 4 - 粗体 (00010 -> 00011)
    {
        0b00011,  // ...##
        0b00111,  // ..###
        0b01011,  // .#.##
        0b10011,  // #..##
        0b11111,  // #####
        0b00011,  // ...##
        0b00011   // ...##
    },
    // 5 - 粗体 (11111 -> 11111)
    {
        0b11111,  // #####
        0b11000,  // ##...
        0b11111,  // #####
        0b00011,  // ...##
        0b00011,  // ...##
        0b11011,  // ##.##
        0b01111   // .####
    },
    // 6 - 粗体 (00110 -> 00111)
    {
        0b00111,  // ..###
        0b01100,  // .##..
        0b11000,  // ##...
        0b11111,  // #####
        0b11011,  // ##.##
        0b11011,  // ##.##
        0b01111   // .####
    },
    // 7 - 粗体 (11111 -> 11111)
    {
        0b11111,  // #####
        0b00011,  // ...##
        0b00110,  // ..##.
        0b01100,  // .##..
        0b01100,  // .##..
        0b01100,  // .##..
        0b01100   // .##..
    },
    // 8 - 粗体 (01110 -> 01111)
    {
        0b01111,  // .####
        0b11011,  // ##.##
        0b11011,  // ##.##
        0b01111,  // .####
        0b11011,  // ##.##
        0b11011,  // ##.##
        0b01111   // .####
    },
    // 9 - 粗体 (01110 -> 01111)
    {
        0b01111,  // .####
        0b11011,  // ##.##
        0b11011,  // ##.##
        0b01111,  // .####
        0b00011,  // ...##
        0b00110,  // ..##.
        0b01100   // .##..
    }
};

// 冒号字符
const uint8_t PROGMEM font5x7_colon[5] = {0x00, 0x36, 0x36, 0x00, 0x00};
const uint8_t PROGMEM font5x7_colon_bold[5] = {0x00, 0x3E, 0x3E, 0x00, 0x00};

// 斜杠字符
const uint8_t PROGMEM font5x7_slash[5] = {0x20, 0x10, 0x08, 0x04, 0x02};
const uint8_t PROGMEM font5x7_slash_bold[5] = {0x30, 0x18, 0x0C, 0x06, 0x03};

#endif // FONT5X7_H
