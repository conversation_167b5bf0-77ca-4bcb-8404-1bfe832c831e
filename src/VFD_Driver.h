#ifndef VFD_DRIVER_H
#define VFD_DRIVER_H

#include <Arduino.h>
#include "Font5x7.h"
#include "FontEffects.h"
#include "font_5x7_digits.h"

// VFD显示器参数
#define VFD_DIGITS 16
#define VFD_CS_PIN 8
#define VFD_DATA_PIN 5
#define VFD_CLK_PIN 3

// 字体风格枚举
enum FontStyle {
    FONT_NORMAL = 0,
    FONT_SQUARE = 1,
    FONT_BOLD = 2
};

class VFD_Driver {
private:
    uint8_t brightness;                 // 亮度控制 (0-100)
    bool isInitialized;                 // 初始化状态
    char displayBuffer[17];             // 显示缓存（16位+结束符）

    // CGRAM缓存管理
    int digitToCGRAM[10];               // 数字到CGRAM地址的映射 (-1表示未加载)
    int cgramContent[8];                // CGRAM内容记录 (-1表示空)
    String lastText;                    // 上次显示的文本（用于缓存优化）

    // 硬件控制
    void chipSelect(bool enable);
    void sendByte(uint8_t data);
    void delay1();

public:
    VFD_Driver();

    // 基础功能
    bool init();
    bool begin();
    void clear();
    void setBrightness(uint8_t level);

    // 显示函数 (基于你的原始代码)
    void writeOneChar(uint8_t position, char character);
    void writeString(uint8_t position, const char* str);
    void showDisplay();

    // 字体相关函数
    void displayText(const char* text);
    void displayTextBold(const char* text);  // 粗体效果显示
    void displayTimeWithFont(const char* timeStr, bool bold);  // 时间专用显示
    void displayTimeWithFontType(const char* timeStr, int fontType);  // 多字体支持
    void displayTimeWithCustomFont(const char* timeStr, FontStyle style);  // 新字体系统
    void writeCGRAM(uint8_t address, const uint8_t* fontData);  // 写入CGRAM
    void loadDynamicFont(const char* text);        // 动态加载字体
    void loadDynamicFontBold(const char* text);    // 动态加载粗体字体
    void displayTextWithCGRAM(const char* text);    // 使用CGRAM显示

    // 基本功能函数
    void resetDisplayBuffer();                      // 重置显示缓存

};

#endif // VFD_DRIVER_H
