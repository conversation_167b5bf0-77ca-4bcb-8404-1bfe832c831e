#ifndef VFD_DRIVER_H
#define VFD_DRIVER_H

#include <Arduino.h>
#include "font_5x7_digits.h"

// VFD显示器参数
#define VFD_DIGITS 16
#define VFD_CS_PIN 8
#define VFD_DATA_PIN 5
#define VFD_CLK_PIN 3

class VFD_Driver {
private:
    uint8_t brightness;
    bool isInitialized;
    char displayBuffer[17];
    int digitToCGRAM[10];
    int cgramContent[8];

    // 硬件控制
    void chipSelect(bool select);
    void sendByte(uint8_t data);
    void delay1();

public:
    VFD_Driver();

    // 基础功能
    bool init();
    void clear();
    void setBrightness(uint8_t level);
    void writeOneChar(uint8_t position, char character);
    void writeString(uint8_t position, const char* str);
    void showDisplay();

    // 核心显示函数
    void displayText(const char* text);
    void displayTimeWithFontType(const char* timeStr, int fontType);
    void writeCGRAM(uint8_t address, const uint8_t* fontData);
    void loadDynamicFont(const char* text);
    void loadDynamicFontBold(const char* text);
    void displayTextWithCGRAM(const char* text);

    // 差分刷新函数 - 解决数字变化牵动其他数字的问题
    void displayTextDiff(const char* text);
    void displayTextWithCGRAMDiff(const char* text);
    void displayTimeWithFontTypeDiff(const char* timeStr, int fontType);
};

#endif // VFD_DRIVER_H
