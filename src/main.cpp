#include <Arduino.h>
#include <WiFi.h>
#include <WebServer.h>
#include <DNSServer.h>
#include <EEPROM.h>
#include <time.h>
#include "VFD_Driver.h"
#include "config.h"

VFD_Driver vfd;
WebServer server(80);
DNSServer dnsServer;

// 按钮引脚定义 - 根据实际硬件代码
#define BUTTON_MODE_PIN 9        // 按钮1 - 模式切换
#define BUTTON_IP_PIN 2          // 按钮2 - IP显示
#define BUTTON_BRIGHTNESS_PIN 10 // 按钮3 - 亮度调节

// 时钟配置结构体
struct ClockConfig {
    char ssid[32];
    char password[64];
    int timezone;
    int displayMode;     // 0=时间/日期/星期, 1=仅时间
    int fontStyle;       // NTP服务器选择
    int fontType;        // 0=常规字体, 1=粗体字体, 2=圆体字体
    int brightness;      // 屏幕亮度 25/50/75/100
    int flippingEffect;  // 翻转效果 0=OFF, 1=ON
    int fontEffect;      // 字体效果 0=Normal, 1=Bold, 2=Round
    int timeFormat;      // 时间格式 12/24
    int dateFormat;      // 日期格式 0=US(MM/DD/YY), 1=UK(DD/MM/YY)
    char alarmTime[6];   // 闹钟时间 "HH:MM"
    int alarmMode;       // 闹钟模式 0=OFF, 1=ON
    bool isConfigured;
};

ClockConfig config;
bool configMode = false;
unsigned long configStartTime = 0;

// 按钮控制变量
unsigned long lastButtonCheck = 0;
bool lastModeButtonState = HIGH;
bool lastIpButtonState = HIGH;
bool lastBrightnessButtonState = HIGH;
int currentBrightness = 75;  // 当前亮度 (0-100)

// 长按调试功能
unsigned long ipButtonPressStart = 0;
bool debugMode = false;

// 按钮状态监控
unsigned long lastButtonStatusPrint = 0;

// 防止频闪变量
String lastDisplayedTime = "";  // 记录上次显示的时间，避免重复刷新

// 字体相关变量
int currentFontType = 0;  // 0=常规字体, 1=粗体字体
unsigned long lastFontSwitch = 0;

// 函数声明
void startConfigMode();
void startWebServer();
void handleRoot();
void handleSave();
void handleStatus();
void handleDisplayMode();
void loadConfig();
void saveConfig();
void connectToWiFi();
void reconnectWiFi();
void syncNTPTime();
void displayFullTime();
void initButtons();
void checkButtons();
void switchDisplayMode();
void showIPAddress();
void enterDebugMode();
void adjustBrightnessLevel();
void switchFontType();
void displayTimeWithFont(const char* timeStr);
void displayMixedTimeAndDate(const char* timePart, const char* datePart);

void setup() {
    Serial.begin(115200);
    delay(500);

    Serial.println("=== VFD WiFi Clock ===");

    // 初始化EEPROM
    EEPROM.begin(512);

    // 初始化按钮
    initButtons();

    // 先加载配置
    loadConfig();

    // 初始化VFD
    if (vfd.init()) {
        Serial.println("VFD initialized!");
        vfd.clear();

        // 从配置中恢复亮度
        vfd.setBrightness(config.brightness);
        Serial.println("Brightness restored from config: " + String(config.brightness) + "%");

        vfd.displayText("VFD CLOCK");
        delay(1000);

    } else {
        Serial.println("VFD init failed!");
        return;
    }

    // 初始化字体类型
    currentFontType = config.fontEffect;  // 使用fontEffect字段

    // 设置WiFi事件监听，诊断断开原因
    WiFi.onEvent([](WiFiEvent_t event, WiFiEventInfo_t info) {
        switch (event) {
            case ARDUINO_EVENT_WIFI_STA_CONNECTED:
                Serial.println("📶 WiFi connected to AP");
                break;
            case ARDUINO_EVENT_WIFI_STA_DISCONNECTED:
                Serial.print("📵 WiFi disconnected, reason: ");
                Serial.println(info.wifi_sta_disconnected.reason);
                break;
            case ARDUINO_EVENT_WIFI_STA_GOT_IP:
                Serial.println("🌐 Got IP address: " + WiFi.localIP().toString());
                break;
            default:
                break;
        }
    });

    // 检查是否已配置WiFi - 实现断电记忆
    if (config.isConfigured && strlen(config.ssid) > 0) {
        Serial.println("WiFi configured, attempting connection...");
        Serial.println("SSID: " + String(config.ssid));
        vfd.displayText("CONNECTING...");

        connectToWiFi();

        if (WiFi.status() == WL_CONNECTED) {
            Serial.println("WiFi connected! Starting normal mode...");
            vfd.displayText("CONNECTED");
            delay(1000);

            // 同步时间
            syncNTPTime();

            // 启动Web服务器
            startWebServer();

            // 直接进入时钟显示模式
            configMode = false;
            Serial.println("Clock ready - Beijing Time (GMT+8)");

        } else {
            Serial.println("WiFi connection failed, starting config mode...");
            vfd.displayText("WIFI FAILED");
            delay(1000);
            startConfigMode();
        }
    } else {
        Serial.println("No WiFi configuration found, starting config mode...");
        vfd.displayText("CONFIG MODE");
        delay(500);
        startConfigMode();
    }
}

void loop() {
    static unsigned long lastTimeUpdate = 0;
    static unsigned long lastWiFiCheck = 0;

    // 始终处理Web请求
    server.handleClient();

    // 检查按钮状态 (每100ms检查一次，稳定可靠)
    if (millis() - lastButtonCheck > 100) {
        lastButtonCheck = millis();
        checkButtons();
    }

    if (configMode) {
        // 配置模式 - 处理DNS请求 (强制门户)
        dnsServer.processNextRequest();

        // 配置超时检查
        if (millis() - configStartTime > 300000) {  // 5分钟超时
            Serial.println("⏰ Config timeout, restarting...");
            vfd.displayText("TIMEOUT RESTART");
            delay(2000);
            ESP.restart();
        }

    } else {
        // 正常时钟模式

        // WiFi状态监控 (每60秒检查一次，减少干扰)
        if (millis() - lastWiFiCheck > 60000) {
            lastWiFiCheck = millis();

            if (WiFi.status() != WL_CONNECTED) {
                Serial.println("⚠️ WiFi disconnected, attempting reconnect...");
                // 暂时显示WiFi状态，然后恢复时间显示
                String currentTime = lastDisplayedTime;
                reconnectWiFi();  // 使用温和的重连方式

                // 如果重连失败，继续显示时间（基于内部时钟）
                if (WiFi.status() != WL_CONNECTED && !currentTime.isEmpty()) {
                    vfd.displayText(currentTime.c_str());
                }
            } else {
                // 定期打印WiFi状态（但不干扰屏幕显示）
                int rssi = WiFi.RSSI();
                Serial.println("📶 WiFi OK - RSSI: " + String(rssi) + " dBm");

                // 如果信号太弱，提前警告
                if (rssi < -75) {
                    Serial.println("⚠️ Weak signal detected (RSSI: " + String(rssi) + "), monitoring closely");
                }
            }
        }

        if (WiFi.status() == WL_CONNECTED) {
            // 时间显示模式
            if (millis() - lastTimeUpdate > 1000) {
                lastTimeUpdate = millis();
                displayFullTime();
            }
        } else {
            // WiFi断开时继续显示时间（基于内部RTC）
            if (millis() - lastTimeUpdate > 1000) {
                lastTimeUpdate = millis();
                displayFullTime();  // 继续显示时间，不显示"NO WIFI"
            }
        }
    }

    delay(10);
}

// 启动配置模式 (带强制门户)
void startConfigMode() {
    configMode = true;
    configStartTime = millis();

    Serial.println("Starting AP mode with Captive Portal...");
    vfd.displayText("STARTING AP...");

    // 启动AP模式
    WiFi.mode(WIFI_AP);
    WiFi.softAP("VFD_Clock_AP", "12345678");
    delay(100);

    // 启动DNS服务器 (强制门户)
    dnsServer.start(53, "*", WiFi.softAPIP());

    Serial.println("AP started: VFD_Clock_AP");
    Serial.print("IP: ");
    Serial.println(WiFi.softAPIP());

    // 设置Web服务器路由 (处理所有请求)
    server.on("/", handleRoot);
    server.on("/save", HTTP_POST, handleSave);
    server.on("/status", handleStatus);
    server.on("/generate_204", handleRoot);  // Android强制门户检测
    server.on("/fwlink", handleRoot);        // Windows强制门户检测
    server.onNotFound(handleRoot);           // 所有其他请求都跳转到配置页面
    server.begin();

    vfd.displayText("WIFI:VFD_Clock_AP");
    Serial.println("Captive Portal started! Connect to VFD_Clock_AP");
}

// 启动Web服务器 (正常模式)
void startWebServer() {
    Serial.println("Starting web server...");

    server.on("/", handleRoot);
    server.on("/save", HTTP_POST, handleSave);
    server.on("/status", handleStatus);
    server.on("/display", HTTP_POST, handleDisplayMode);
    server.onNotFound(handleRoot);
    server.begin();

    Serial.println("Web server started");
    Serial.print("Visit: http://");
    Serial.println(WiFi.localIP());
}

// 加载配置
void loadConfig() {
    EEPROM.get(0, config);

    // 检查配置是否有效
    if (config.timezone < -12 || config.timezone > 12) {
        Serial.println("Invalid config detected, setting defaults...");

        // 清空所有配置
        memset(&config, 0, sizeof(config));

        // 设置默认值
        strcpy(config.ssid, "");
        strcpy(config.password, "123456aa..");  // 默认密码包含两个点
        strcpy(config.alarmTime, "07:00");
        config.timezone = 8;         // GMT+8 北京时间
        config.displayMode = 0;      // 时间/日期/星期模式
        config.fontStyle = 0;        // 自动NTP
        config.fontType = 0;         // 常规字体
        config.brightness = 70;      // 高亮度 (对应新的等级3)
        config.flippingEffect = 0;   // 翻转效果关闭
        config.fontEffect = 0;       // 普通字体效果
        config.timeFormat = 24;      // 24小时制
        config.dateFormat = 0;       // 美国日期格式
        config.alarmMode = 0;        // 闹钟关闭
        config.isConfigured = false;

        saveConfig();
        Serial.println("Default config saved");
    }

    Serial.println("Config loaded:");
    Serial.println("SSID: " + String(config.ssid));
    Serial.println("Display mode: " + String(config.displayMode));
}

// 保存配置
void saveConfig() {
    EEPROM.put(0, config);
    EEPROM.commit();
    Serial.println("Config saved");
}

// Web配置页面 - 使用新的LGL Clock Config界面
void handleRoot() {
    String html = "<!DOCTYPE html><html><head>";
    html += "<meta charset='UTF-8'>";
    html += "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
    html += "<title>LGL Clock Config</title>";
    html += "<style>";

    // 更紧凑居中的CSS样式
    html += "body { font-family: \"Inter Tight\", Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 15px; text-align: center; background: #f8f9fa; }";
    html += ".subtitle { font-size: 12px; color: #666; margin-bottom: 20px; font-style: italic; }";
    html += ".section { margin-bottom: 20px; border: 1px solid #ddd; padding: 16px; border-radius: 8px; background: white; }";
    html += ".tab { display: none; }";
    html += ".tab.active { display: block; }";
    html += ".tabs { display: flex; margin-bottom: 12px; justify-content: center; gap: 4px; }";
    html += ".tab-btn { padding: 8px 16px; cursor: pointer; border: 1px solid #ccc; font-size: 13px; border-radius: 4px; font-weight: 400; min-width: 120px; }";
    html += ".tab-btn.active { background-color: #4CAF50; color: white; }";
    html += "label { display: inline-block; width: 80px; margin-bottom: 8px; text-align: right; padding-right: 12px; font-size: 13px; }";
    html += "input, select { margin-bottom: 8px; padding: 4px; width: 140px; font-size: 13px; }";
    html += ".wifi-input { width: 200px; }";
    html += "input[type=\"radio\"], input[type=\"checkbox\"] { width: auto; }";
    html += "input[type=\"range\"] { width: 120px; }";
    html += ".row { display: flex; align-items: center; margin-bottom: 10px; justify-content: center; max-width: 400px; margin-left: auto; margin-right: auto; }";
    html += ".row-half { display: flex; align-items: center; margin-bottom: 8px; justify-content: flex-start; width: 48%; }";
    html += ".row-container { display: flex; justify-content: space-between; max-width: 400px; margin-left: auto; margin-right: auto; margin-bottom: 8px; }";
    html += ".centered { text-align: center; }";
    html += ".centered-container { display: flex; flex-direction: column; align-items: center; }";
    html += ".button-row { display: flex; justify-content: center; gap: 12px; margin-top: 16px; }";
    html += ".time-sync-options { display: flex; flex-direction: row; justify-content: center; gap: 8px; margin-bottom: 20px; }";
    html += ".tab-content { margin-top: 16px; }";
    html += ".options-container { display: flex; flex-direction: column; align-items: center; }";
    html += ".option-row { display: flex; align-items: center; margin-bottom: 4px; }";
    html += ".btn-container { margin-top: 16px; display: flex; justify-content: center; gap: 12px; }";
    html += ".submit-btn, .wifi-btn, .settings-btn { padding: 10px 20px; background-color: #4CAF50; color: white; border: none; cursor: pointer; border-radius: 4px; font-size: 13px; font-weight: 400; min-width: 120px; }";
    html += ".settings-btn { background-color: #6c757d; }";
    html += "h1 { font-size: 20px; margin-bottom: 8px; text-transform: uppercase; letter-spacing: 1px; }";
    html += "h2 { font-size: 16px; margin-bottom: 12px; border-bottom: 1px solid #eee; padding-bottom: 8px; text-align: center; }";
    html += ".form-group { width: 100%; max-width: 400px; margin: 0 auto; text-align: center; }";
    html += ".footer { margin-top: 30px; padding: 15px; border-top: 1px solid #eee; text-align: center; color: #666; font-size: 12px; }";
    html += ".tagline { font-style: italic; margin-bottom: 8px; }";
    html += ".copyright { }";
    html += ".timezone-hint { font-size: 11px; color: #666; font-style: italic; margin: 8px auto; text-align: center; max-width: 350px; line-height: 1.3; background-color: #f0f8f0; padding: 8px 12px; border-radius: 4px; border-left: 3px solid #4CAF50; }";

    html += "</style></head><body>";

    html += "<h1>LGLSTUDIO VFD Clock Setup</h1>";
    html += "<div class='subtitle'>Not just a clock, but a little companion on your desk, keeping you company through the boring days.</div>";

    html += "<form action='/save' method='POST'>";

    // 1. WiFi Settings
    html += "<div class='section'>";
    html += "<h2>WiFi Settings</h2>";
    html += "<div class='form-group'>";
    html += "<div class='row'>";
    html += "<label for='wifi_name'>SSID:</label>";
    html += "<input type='text' name='ssid' id='wifi_name' class='wifi-input' value='";

    // 安全输出WiFi名称，清理任何残留
    String ssidStr = String(config.ssid);
    for (int i = 0; i < ssidStr.length(); i++) {
        char c = ssidStr.charAt(i);
        if (c >= 32 && c <= 126) {
            html += c;
        }
    }

    html += "' required>";
    html += "</div>";
    html += "<div class='row'>";
    html += "<label for='wifi_password'>Password:</label>";
    html += "<input type='password' name='password' id='wifi_password' class='wifi-input' value='";
    html += String(config.password);  // 显示已保存的密码
    html += "' required>";
    html += "</div>";
    html += "</div>";
    html += "<div class='btn-container'>";
    html += "<button type='submit' class='submit-btn'>Save Wi-Fi</button>";
    html += "</div>";
    html += "</div>";

    // 2. Time Synchronization
    html += "<div class='section'>";
    html += "<h2>Time Synchronization</h2>";
    html += "<div class='time-sync-options'>";
    html += "<div class='tab-btn active' onclick='showTimeTab(\"auto\")'>Auto Sync</div>";
    html += "<div class='tab-btn' onclick='showTimeTab(\"offline\")'>Offline Mode</div>";
    html += "</div>";
    html += "<div class='advanced-options'>";
    html += "<div class='tab-btn' onclick='showTimeTab(\"advanced\")'>Advanced Custom Mode</div>";
    html += "</div>";

    // Auto Sync Mode
    html += "<div class='tab active tab-content' id='auto-tab'>";
    html += "<div class='form-group'>";
    html += "<div class='row'>";
    html += "<label for='timezone'>Timezone:</label>";
    html += "<select name='timezone' id='timezone'>";
    for (int i = -12; i <= 12; i++) {
        html += "<option value='" + String(i) + "'";
        if (i == config.timezone) html += " selected";
        html += ">UTC";
        if (i >= 0) html += "+";
        html += String(i) + "</option>";
    }
    html += "</select>";
    html += "</div>";
    html += "<div class='row'>";
    html += "<label for='ntp_server'>NTP Server:</label>";
    html += "<select name='ntpServer' id='ntp_server'>";
    html += "<option value='0'";
    if (config.fontStyle == 0) html += " selected";
    html += ">pool.ntp.org</option>";
    html += "<option value='1'";
    if (config.fontStyle == 1) html += " selected";
    html += ">ntp.aliyun.com</option>";
    html += "<option value='2'";
    if (config.fontStyle == 2) html += " selected";
    html += ">time.windows.com</option>";
    html += "</select>";
    html += "</div>";
    html += "</div>";
    html += "<div class='btn-container'>";
    html += "<button type='submit' class='submit-btn'>Save Time Settings</button>";
    html += "</div>";
    html += "</div>";

    // Advanced Custom Mode - 完整DST功能
    html += "<div class='tab tab-content' id='advanced-tab'>";
    html += "<div class='form-group'>";

    // Standard Timezone
    html += "<div class='row'>";
    html += "<label for='std_timezone'>Standard Timezone:</label>";
    html += "<select name='stdTimezone' id='std_timezone' style='width: 140px;'>";
    for (int i = -12; i <= 12; i++) {
        html += "<option value='" + String(i) + "'";
        if (i == config.timezone) html += " selected";
        html += ">";
        if (i >= 0) html += "+";
        html += String(i) + "</option>";
    }
    html += "</select>";
    html += "</div>";

    // DST Timezone
    html += "<div class='row'>";
    html += "<label for='dst_timezone'>DST Timezone:</label>";
    html += "<select name='dstTimezone' id='dst_timezone' style='width: 140px;'>";
    for (int i = -12; i <= 12; i++) {
        html += "<option value='" + String(i) + "'>";
        if (i >= 0) html += "+";
        html += String(i) + "</option>";
    }
    html += "</select>";
    html += "</div>";

    // DST说明
    html += "<div class='timezone-hint'>";
    html += "Example: If std timezone is -6, DST moves +1hr, set DST timezone to -5.";
    html += "</div>";

    // NTP Server
    html += "<div class='row'>";
    html += "<label for='ntp_server_adv'>NTP Srv:</label>";
    html += "<select name='ntpServerAdv' id='ntp_server_adv' style='width: 140px;'>";
    html += "<option value='pool.ntp.org'>pool.ntp.org</option>";
    html += "<option value='ntp.aliyun.com'>ntp.aliyun.com</option>";
    html += "<option value='time.windows.com'>time.windows.com</option>";
    html += "<option value='time.apple.com'>time.apple.com</option>";
    html += "</select>";
    html += "</div>";

    // NTP Offset
    html += "<div class='row'>";
    html += "<label for='ntp_offset'>NTP Offset:</label>";
    html += "<select name='ntpOffset' id='ntp_offset' style='width: 140px;'>";
    html += "<option value='0' selected>0s</option>";
    html += "<option value='0.1'>0.1s</option>";
    html += "<option value='0.5'>0.5s</option>";
    html += "<option value='1'>1s</option>";
    html += "<option value='-0.1'>-0.1s</option>";
    html += "<option value='-0.5'>-0.5s</option>";
    html += "<option value='-1'>-1s</option>";
    html += "</select>";
    html += "</div>";

    html += "</div>";

    // DST Start Rule
    html += "<h3 style='font-size: 14px; margin: 15px 0 10px 0; text-align: center;'>DST Start Rule</h3>";
    html += "<div class='form-group'>";

    // Month and Week in one row
    html += "<div class='row-container'>";
    html += "<div class='row-half'>";
    html += "<select name='dstStartMonth' id='dst_start_month' style='width: 120px;'>";
    html += "<option value='0'>No DST</option>";
    html += "<option value='1'>Month: January</option>";
    html += "<option value='2'>Month: February</option>";
    html += "<option value='3'>Month: March</option>";
    html += "<option value='4'>Month: April</option>";
    html += "<option value='5'>Month: May</option>";
    html += "<option value='6'>Month: June</option>";
    html += "<option value='7'>Month: July</option>";
    html += "<option value='8'>Month: August</option>";
    html += "<option value='9'>Month: September</option>";
    html += "<option value='10'>Month: October</option>";
    html += "<option value='11'>Month: November</option>";
    html += "<option value='12'>Month: December</option>";
    html += "</select>";
    html += "</div>";
    html += "<div class='row-half'>";
    html += "<select name='dstStartWeek' id='dst_start_week' style='width: 120px;'>";
    html += "<option value='1'>Week: 1st</option>";
    html += "<option value='2'>Week: 2nd</option>";
    html += "<option value='3'>Week: 3rd</option>";
    html += "<option value='4'>Week: 4th</option>";
    html += "<option value='last'>Week: Last</option>";
    html += "</select>";
    html += "</div>";
    html += "</div>";

    // Day and Time in one row
    html += "<div class='row-container'>";
    html += "<div class='row-half'>";
    html += "<select name='dstStartDay' id='dst_start_day' style='width: 120px;'>";
    html += "<option value='0'>Day: Sunday</option>";
    html += "<option value='1'>Day: Monday</option>";
    html += "<option value='2'>Day: Tuesday</option>";
    html += "<option value='3'>Day: Wednesday</option>";
    html += "<option value='4'>Day: Thursday</option>";
    html += "<option value='5'>Day: Friday</option>";
    html += "<option value='6'>Day: Saturday</option>";
    html += "</select>";
    html += "</div>";
    html += "<div class='row-half'>";
    html += "<input type='time' name='dstStartTime' id='dst_start_time' value='02:00' style='width: 120px;' placeholder='Time'>";
    html += "</div>";
    html += "</div>";
    html += "</div>";

    // DST End Rule
    html += "<h3 style='font-size: 14px; margin: 15px 0 10px 0; text-align: center;'>DST End Rule</h3>";
    html += "<div class='form-group'>";

    // Month and Week in one row
    html += "<div class='row-container'>";
    html += "<div class='row-half'>";
    html += "<select name='dstEndMonth' id='dst_end_month' style='width: 120px;'>";
    html += "<option value='0'>No DST</option>";
    html += "<option value='1'>Month: January</option>";
    html += "<option value='2'>Month: February</option>";
    html += "<option value='3'>Month: March</option>";
    html += "<option value='4'>Month: April</option>";
    html += "<option value='5'>Month: May</option>";
    html += "<option value='6'>Month: June</option>";
    html += "<option value='7'>Month: July</option>";
    html += "<option value='8'>Month: August</option>";
    html += "<option value='9'>Month: September</option>";
    html += "<option value='10'>Month: October</option>";
    html += "<option value='11'>Month: November</option>";
    html += "<option value='12'>Month: December</option>";
    html += "</select>";
    html += "</div>";
    html += "<div class='row-half'>";
    html += "<select name='dstEndWeek' id='dst_end_week' style='width: 120px;'>";
    html += "<option value='1'>Week: 1st</option>";
    html += "<option value='2'>Week: 2nd</option>";
    html += "<option value='3'>Week: 3rd</option>";
    html += "<option value='4'>Week: 4th</option>";
    html += "<option value='last'>Week: Last</option>";
    html += "</select>";
    html += "</div>";
    html += "</div>";

    // Day and Time in one row
    html += "<div class='row-container'>";
    html += "<div class='row-half'>";
    html += "<select name='dstEndDay' id='dst_end_day' style='width: 120px;'>";
    html += "<option value='0'>Day: Sunday</option>";
    html += "<option value='1'>Day: Monday</option>";
    html += "<option value='2'>Day: Tuesday</option>";
    html += "<option value='3'>Day: Wednesday</option>";
    html += "<option value='4'>Day: Thursday</option>";
    html += "<option value='5'>Day: Friday</option>";
    html += "<option value='6'>Day: Saturday</option>";
    html += "</select>";
    html += "</div>";
    html += "<div class='row-half'>";
    html += "<input type='time' name='dstEndTime' id='dst_end_time' value='02:00' style='width: 120px;' placeholder='Time'>";
    html += "</div>";
    html += "</div>";
    html += "</div>";

    html += "<div class='btn-container'>";
    html += "<button type='submit' class='submit-btn'>Save Settings & Reboot</button>";
    html += "</div>";
    html += "</div>";

    // Offline Mode
    html += "<div class='tab tab-content' id='offline-tab'>";
    html += "<div class='form-group'>";
    html += "<div class='row'>";
    html += "<label for='wifi_enable'>WiFi Module:</label>";
    html += "<select name='wifiEnable' id='wifi_enable'>";
    html += "<option value='1' selected>Enable</option>";
    html += "<option value='0'>Disable</option>";
    html += "</select>";
    html += "</div>";
    html += "<div class='row'>";
    html += "<label for='manual_time'>Set Time:</label>";
    html += "<input type='time' name='manualTime' id='manual_time'>";
    html += "</div>";
    html += "<div class='row'>";
    html += "<label for='manual_date'>Set Date:</label>";
    html += "<input type='date' name='manualDate' id='manual_date'>";
    html += "</div>";
    html += "</div>";
    html += "<div class='btn-container'>";
    html += "<button type='submit' class='submit-btn'>Save Offline Settings</button>";
    html += "</div>";
    html += "</div>";

    html += "</div>";

    // 3. Display & Alarm Settings
    html += "<div class='section'>";
    html += "<h2>Display & Alarm</h2>";
    html += "<div class='form-group'>";

    // Screen Brightness
    html += "<div class='row'>";
    html += "<label for='screen_brightness'>Brightness:</label>";
    html += "<select name='brightness' id='screen_brightness'>";
    html += "<option value='25'>Low</option>";
    html += "<option value='50'>Medium</option>";
    html += "<option value='75' selected>High</option>";
    html += "<option value='100'>Max</option>";
    html += "</select>";
    html += "</div>";

    // Flipping Effect
    html += "<div class='row'>";
    html += "<label for='flipping_effect'>Flip Effect:</label>";
    html += "<select name='flippingEffect' id='flipping_effect'>";
    html += "<option value='0' selected>OFF</option>";
    html += "<option value='1'>ON</option>";
    html += "</select>";
    html += "</div>";

    // Time Font Effect (只保留3种字体)
    html += "<div class='row'>";
    html += "<label for='font_effect'>Font Style:</label>";
    html += "<select name='fontEffect' id='font_effect'>";
    html += "<option value='0'";
    if (config.fontEffect == 0) html += " selected";
    html += ">Normal</option>";
    html += "<option value='1'";
    if (config.fontEffect == 1) html += " selected";
    html += ">Square</option>";
    html += "<option value='2'";
    if (config.fontEffect == 2) html += " selected";
    html += ">Bold</option>";
    html += "</select>";
    html += "</div>";

    // Time Format
    html += "<div class='row'>";
    html += "<label for='time_format'>Time Format:</label>";
    html += "<select name='timeFormat' id='time_format'>";
    html += "<option value='24' selected>24H</option>";
    html += "<option value='12'>12H</option>";
    html += "</select>";
    html += "</div>";

    // Display Mode
    html += "<div class='row'>";
    html += "<label for='display_mode'>Display Mode:</label>";
    html += "<select name='displayMode' id='display_mode'>";
    html += "<option value='0'";
    if (config.displayMode == 0) html += " selected";
    html += ">Time+Date</option>";
    html += "<option value='1'";
    if (config.displayMode == 1) html += " selected";
    html += ">Time Only</option>";
    html += "</select>";
    html += "</div>";

    // Date Format
    html += "<div class='row'>";
    html += "<label for='date_format'>Date Format:</label>";
    html += "<select name='dateFormat' id='date_format'>";
    html += "<option value='0' selected>US (MM/DD/YY)</option>";
    html += "<option value='1'>UK (DD/MM/YY)</option>";
    html += "</select>";
    html += "</div>";

    // Alarm Time
    html += "<div class='row'>";
    html += "<label for='alarm_time'>Alarm Time:</label>";
    html += "<input type='time' name='alarmTime' id='alarm_time' value='07:00'>";
    html += "</div>";

    // Alarm Mode
    html += "<div class='row'>";
    html += "<label for='alarm_mode'>Alarm:</label>";
    html += "<select name='alarmMode' id='alarm_mode'>";
    html += "<option value='0' selected>OFF</option>";
    html += "<option value='1'>ON</option>";
    html += "</select>";
    html += "</div>";

    html += "</div>";
    html += "<div class='btn-container'>";
    html += "<button type='submit' class='submit-btn'>Save Display Settings</button>";
    html += "</div>";
    html += "</div>";

    // Footer
    html += "<div class='footer'>";
    html += "<p class='tagline'>VFD Clock - A desktop companion to brighten your days.</p>";
    html += "<p class='copyright'>© 2024 LGL Studio. All rights reserved.</p>";
    html += "</div>";

    html += "</form>";

    // JavaScript
    html += "<script>";
    html += "function showTimeTab(tabName) {";
    html += "  document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));";
    html += "  document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));";
    html += "  document.getElementById(tabName + '-tab').classList.add('active');";
    html += "  event.target.classList.add('active');";
    html += "}";
    html += "</script>";

    html += "</body></html>";

    server.send(200, "text/html", html);
}

// 处理配置保存并连接WiFi
void handleSave() {
    String ssid = server.arg("ssid");
    String password = server.arg("password");
    int timezone = server.arg("timezone").toInt();
    int displayMode = server.arg("displayMode").toInt();
    int ntpServer = server.arg("ntpServer").toInt();

    // 新增的显示和闹钟设置
    int brightness = server.arg("brightness").toInt();
    int flippingEffect = server.arg("flippingEffect").toInt();
    int fontEffect = server.arg("fontEffect").toInt();
    int timeFormat = server.arg("timeFormat").toInt();
    int dateFormat = server.arg("dateFormat").toInt();
    String alarmTime = server.arg("alarmTime");
    int alarmMode = server.arg("alarmMode").toInt();

    // 智能保存配置 - 只更新提交的字段
    if (ssid.length() > 0) {
        ssid.toCharArray(config.ssid, sizeof(config.ssid));
        Serial.println("Updated SSID: " + ssid);
    }
    if (password.length() > 0) {
        password.toCharArray(config.password, sizeof(config.password));
        Serial.println("Updated Password: [HIDDEN]");
    }
    if (alarmTime.length() > 0) {
        alarmTime.toCharArray(config.alarmTime, sizeof(config.alarmTime));
    }

    // 检测时区是否变化
    bool timezoneChanged = (config.timezone != timezone);

    config.timezone = timezone;
    config.displayMode = displayMode;
    config.fontStyle = ntpServer;  // 复用fontStyle字段存储NTP服务器选择
    config.brightness = brightness;
    config.flippingEffect = flippingEffect;
    config.fontEffect = fontEffect;
    config.timeFormat = timeFormat;
    config.dateFormat = dateFormat;
    config.alarmMode = alarmMode;
    config.isConfigured = true;

    if (timezoneChanged) {
        Serial.println("Timezone changed from " + String(config.timezone) + " to " + String(timezone));
    }

    saveConfig();

    Serial.println("Configuration saved:");
    Serial.println("SSID: " + String(config.ssid));
    Serial.println("Timezone: GMT" + String(config.timezone >= 0 ? "+" : "") + String(config.timezone));
    Serial.println("Display Mode: " + String(config.displayMode));
    Serial.println("NTP Server: " + String(config.fontStyle));

    // 发送连接中页面 (英文版)
    String html = F(R"(
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>Connecting to WiFi</title>
<style>
body{font-family:"Inter Tight",Arial,sans-serif;margin:20px;background:#f8f9fa;text-align:center}
.container{background:white;padding:30px;border-radius:8px;max-width:400px;margin:0 auto;box-shadow:0 2px 10px rgba(0,0,0,0.1)}
.connecting{color:#4CAF50;font-size:18px;margin-bottom:15px;font-weight:400}
.spinner{border:4px solid #f3f3f3;border-top:4px solid #4CAF50;border-radius:50%;width:40px;height:40px;animation:spin 1s linear infinite;margin:20px auto}
@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}
.info{color:#666;font-size:14px;margin:10px 0}
.note{color:#999;font-size:12px;margin-top:20px}
</style>
</head>
<body>
<div class="container">
<div class="connecting">📡 Connecting to WiFi...</div>
<div class="spinner"></div>
<p class="info">Network: )");

    html += ssid;
    html += F(R"(</p>
<p class="info">Please wait for the clock to display time</p>
<p class="note">Access the clock via IP address after connection</p>
<p class="note">If connection fails, device will restart in config mode</p>
</div>
<script>
setTimeout(function(){
    window.location.href = '/status';
}, 8000);
</script>
</body>
</html>
)");

    server.send(200, "text/html", html);

    // 检查WiFi配置是否完整
    if (strlen(config.ssid) == 0) {
        Serial.println("No WiFi SSID configured, staying in config mode");
        server.send(200, "text/html", "Configuration saved, but no WiFi configured. Please set WiFi credentials first.");
        return;
    }

    // 发送保存成功响应给用户
    String successHtml = "<!DOCTYPE html><html><head>";
    successHtml += "<meta charset='UTF-8'>";
    successHtml += "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
    successHtml += "<title>Settings Saved</title>";
    successHtml += "<style>body{font-family:Arial;text-align:center;padding:50px;background:#f0f0f0;}";
    successHtml += ".container{background:white;padding:30px;border-radius:10px;box-shadow:0 4px 6px rgba(0,0,0,0.1);max-width:400px;margin:0 auto;}";
    successHtml += ".success{color:#28a745;font-size:24px;margin-bottom:20px;}";
    successHtml += ".status{font-size:18px;margin:10px 0;}</style></head><body>";
    successHtml += "<div class='container'>";
    successHtml += "<div class='success'>✅ Settings Saved!</div>";
    successHtml += "<div class='status'>Connecting to WiFi...</div>";
    successHtml += "<div class='status'>Please wait, device will restart automatically.</div>";
    successHtml += "</div>";
    successHtml += "<script>setTimeout(function(){window.location.href='/status';}, 15000);</script>";
    successHtml += "</body></html>";

    server.send(200, "text/html", successHtml);
    delay(1000);  // 确保响应发送完成

    // 尝试连接WiFi，增强稳定性
    Serial.println("Connecting to WiFi: " + String(config.ssid));
    vfd.displayText("CONNECTING...");

    // 先断开所有连接，清理状态
    WiFi.disconnect(true);
    delay(1000);

    // 设置WiFi参数以提高稳定性
    WiFi.mode(WIFI_STA);
    WiFi.setAutoReconnect(true);
    WiFi.persistent(true);
    WiFi.setSleep(false);  // 禁用WiFi休眠

    WiFi.begin(config.ssid, config.password);

    // 增加等待时间，提供更多状态反馈
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 40) {  // 增加到40次尝试（20秒）
        delay(500);
        Serial.print(".");
        attempts++;

        // 每5次尝试更新一次屏幕显示
        if (attempts % 5 == 0) {
            String statusMsg = "CONNECTING..." + String(attempts/5);
            vfd.displayText(statusMsg.c_str());
        }
    }

    if (WiFi.status() == WL_CONNECTED) {
        Serial.println("\n✅ WiFi connected successfully!");
        vfd.displayText("CONNECTED");
        delay(1500);

        // 显示IP地址
        String ipDisplay = "IP:" + WiFi.localIP().toString();
        vfd.displayText(ipDisplay.c_str());
        delay(2000);

        // 强制重新同步时间
        Serial.println("Syncing time after configuration...");
        vfd.displayText("SYNC TIME...");
        syncNTPTime();
        vfd.displayText("TIME SYNCED");
        delay(1000);

        // 停止配置模式，切换到正常模式
        configMode = false;
        dnsServer.stop();
        startWebServer();
        Serial.println("Normal mode started. Web server at: " + WiFi.localIP().toString());

        // 开始显示时间
        vfd.displayText("CLOCK READY");
        delay(1000);

    } else {
        Serial.println("\n❌ WiFi connection failed after 40 attempts!");
        vfd.displayText("CONN FAILED");
        delay(2000);

        vfd.displayText("CHECK WIFI");
        delay(2000);

        vfd.displayText("RESTARTING...");
        delay(2000);

        // 重启设备，让用户重新配置
        ESP.restart();
    }
}

// 状态页面 (英文版，与配置页面风格一致)
void handleStatus() {
    time_t now = time(nullptr);
    struct tm* timeinfo = localtime(&now);

    String currentTime = "Not Synced";
    if (timeinfo->tm_year > 100) {
        char timeStr[20];
        snprintf(timeStr, sizeof(timeStr), "%02d:%02d:%02d %02d/%02d",
                timeinfo->tm_hour, timeinfo->tm_min, timeinfo->tm_sec,
                timeinfo->tm_mon + 1, timeinfo->tm_mday);
        currentTime = String(timeStr);
    }

    String displayModeStr = (config.displayMode == 0) ? "Time + Date" : "Time Only";
    String wifiStatus = (WiFi.status() == WL_CONNECTED) ? "Connected" : "Disconnected";
    String timezoneStr = "GMT" + String(config.timezone >= 0 ? "+" : "") + String(config.timezone);

    String html = F(R"(
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width,initial-scale=1">
<title>VFD Clock Status</title>
<style>
body{font-family:"Inter Tight",Arial,sans-serif;margin:20px;background:#f8f9fa;text-align:center}
.container{background:white;padding:30px;border-radius:8px;max-width:600px;margin:0 auto;box-shadow:0 2px 10px rgba(0,0,0,0.1)}
h1{color:#333;font-size:20px;margin-bottom:8px;text-transform:uppercase;letter-spacing:1px}
h2{color:#333;font-size:16px;margin-bottom:12px;border-bottom:1px solid #eee;padding-bottom:8px;text-align:center}
.section{background:#f8f9fa;padding:20px;border-radius:8px;margin:20px 0;text-align:left}
.status-grid{display:grid;grid-template-columns:1fr 1fr;gap:15px;margin:15px 0}
.status-item{background:white;padding:15px;border-radius:6px;border-left:4px solid #4CAF50}
.status-label{font-weight:600;color:#666;font-size:12px;text-transform:uppercase;margin-bottom:5px}
.status-value{font-size:16px;color:#333;font-weight:400}
.control-section{background:#e8f5e8;padding:20px;border-radius:8px;margin:20px 0}
.button-row{display:flex;justify-content:center;gap:12px;margin:15px 0}
.btn{background:#4CAF50;color:white;padding:10px 20px;border:none;border-radius:4px;cursor:pointer;font-weight:400;min-width:120px}
.btn:hover{background:#45a049}
.btn-secondary{background:#007cba}
.btn-secondary:hover{background:#005a87}
.back-link{display:inline-block;margin-top:20px;color:#007cba;text-decoration:none;font-weight:400}
.back-link:hover{text-decoration:underline}
.footer{margin-top:30px;padding:15px;border-top:1px solid #eee;text-align:center;color:#666;font-size:12px}
</style>
</head>
<body>
<div class="container">
<h1>LGLSTUDIO VFD Clock Status</h1>

<div class="section">
<h2>📊 Current Status</h2>
<div class="status-grid">
<div class="status-item">
<div class="status-label">Current Time</div>
<div class="status-value">)");

    html += currentTime;
    html += F(R"(</div>
</div>
<div class="status-item">
<div class="status-label">WiFi Status</div>
<div class="status-value">)");
    html += wifiStatus;
    html += F(R"(</div>
</div>
<div class="status-item">
<div class="status-label">Network</div>
<div class="status-value">)");
    html += String(config.ssid);
    html += F(R"(</div>
</div>
<div class="status-item">
<div class="status-label">IP Address</div>
<div class="status-value">)");
    html += WiFi.localIP().toString();
    html += F(R"(</div>
</div>
<div class="status-item">
<div class="status-label">Timezone</div>
<div class="status-value">)");
    html += timezoneStr;
    html += F(R"(</div>
</div>
<div class="status-item">
<div class="status-label">Display Mode</div>
<div class="status-value">)");
    html += displayModeStr;
    html += F(R"(</div>
</div>
</div>
</div>

<div class="control-section">
<h2>⚡ Quick Controls</h2>
<div class="button-row">
<form action="/display" method="POST" style="display:inline">
<input type="hidden" name="mode" value="0">
<button type="submit" class="btn">Time + Date</button>
</form>
<form action="/display" method="POST" style="display:inline">
<input type="hidden" name="mode" value="1">
<button type="submit" class="btn">Time Only</button>
</form>
</div>
</div>

<a href="/" class="back-link">← Back to Configuration</a>

<div class="footer">
<div>VFD Clock by LGLSTUDIO</div>
<div>Status refreshes automatically every 30 seconds</div>
</div>
</div>

<script>
// Auto refresh every 30 seconds
setTimeout(function(){ window.location.reload(); }, 30000);
</script>
</body>
</html>
)");

    server.send(200, "text/html", html);
}

// 处理显示模式切换 (英文版)
void handleDisplayMode() {
    int newMode = server.arg("mode").toInt();
    config.displayMode = newMode;
    saveConfig();

    String modeStr = (newMode == 0) ? "Time + Date" : "Time Only";

    String html = F(R"(
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>Display Mode Changed</title>
<style>
body{font-family:"Inter Tight",Arial,sans-serif;margin:20px;background:#f8f9fa;text-align:center}
.container{background:white;padding:30px;border-radius:8px;max-width:400px;margin:0 auto;box-shadow:0 2px 10px rgba(0,0,0,0.1)}
.success{color:#4CAF50;font-size:18px;margin-bottom:15px;font-weight:400}
.mode{color:#333;font-size:16px;margin:15px 0}
.back-link{color:#007cba;text-decoration:none;font-weight:400}
.back-link:hover{text-decoration:underline}
</style>
</head>
<body>
<div class="container">
<div class="success">✅ Display Mode Changed Successfully!</div>
<div class="mode">New Mode: )");

    html += modeStr;
    html += F(R"(</div>
<p><a href="/status" class="back-link">View Status</a></p>
</div>
<script>
setTimeout(function(){ window.location.href = '/status'; }, 2000);
</script>
</body>
</html>
)");

    server.send(200, "text/html", html);
}

// WiFi连接 - 增强稳定性和用户反馈
void connectToWiFi() {
    if (strlen(config.ssid) == 0) return;

    static unsigned long lastConnectAttempt = 0;
    static int reconnectAttempts = 0;

    // 限制重连频率，避免频繁尝试
    if (millis() - lastConnectAttempt < 30000) {  // 30秒间隔
        return;
    }

    lastConnectAttempt = millis();
    reconnectAttempts++;

    Serial.println("🔄 WiFi reconnect attempt #" + String(reconnectAttempts));
    Serial.println("📡 Connecting to: " + String(config.ssid));

    // 显示连接状态
    vfd.displayText("RECONNECTING...");

    // 完全重置WiFi状态
    WiFi.disconnect(true);
    delay(500);

    // 设置WiFi参数 - 增强稳定性
    WiFi.mode(WIFI_STA);
    WiFi.setAutoReconnect(true);  // 启用自动重连
    WiFi.persistent(true);        // 保存WiFi配置

    // 增强稳定性设置
    WiFi.setSleep(false);         // 禁用WiFi休眠，保持连接
    WiFi.setTxPower(WIFI_POWER_19_5dBm);  // 设置最大发射功率

    Serial.println("📡 WiFi settings: Sleep=OFF, Power=MAX");

    WiFi.begin(config.ssid, config.password);

    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 30) {  // 增加到30次尝试
        delay(500);
        Serial.print(".");
        attempts++;

        // 每5次尝试更新屏幕显示
        if (attempts % 5 == 0) {
            String statusMsg = "RECONNECT " + String(attempts/5);
            vfd.displayText(statusMsg.c_str());
        }

        // 检查是否需要中断连接尝试
        if (attempts > 20) {
            Serial.print("⚠️");
        }
    }

    if (WiFi.status() == WL_CONNECTED) {
        Serial.println("\n✅ WiFi reconnected!");
        Serial.println("📍 IP: " + WiFi.localIP().toString());
        Serial.println("📶 RSSI: " + String(WiFi.RSSI()) + " dBm");

        vfd.displayText("WIFI OK");
        delay(1000);

        reconnectAttempts = 0;  // 重置重连计数

        // 同步时间
        if (time(nullptr) < 100000) {
            vfd.displayText("SYNC TIME...");
            syncNTPTime();
        }
    } else {
        Serial.println("\n❌ WiFi connection failed!");
        Serial.println("🔄 Will retry in 30 seconds...");

        vfd.displayText("WIFI FAILED");
        delay(2000);

        // 如果重连次数过多，重启设备
        if (reconnectAttempts > 8) {  // 减少重试次数，更快重启
            Serial.println("🔄 Too many failed attempts, restarting...");
            vfd.displayText("RESTARTING...");
            delay(3000);
            ESP.restart();
        }
    }
}

// 温和的WiFi重连函数 (不干扰屏幕显示)
void reconnectWiFi() {
    static unsigned long lastReconnectAttempt = 0;
    static int quickReconnectAttempts = 0;

    // 避免过于频繁的重连尝试 (至少间隔30秒)
    if (millis() - lastReconnectAttempt < 30000) {
        return;
    }

    lastReconnectAttempt = millis();
    quickReconnectAttempts++;

    Serial.println("🔄 Attempting gentle WiFi reconnect #" + String(quickReconnectAttempts));

    // 首先尝试温和的重连方式
    WiFi.reconnect();

    // 短暂等待，不阻塞主循环
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 10) {  // 减少等待时间
        delay(300);
        Serial.print(".");
        attempts++;
    }

    if (WiFi.status() == WL_CONNECTED) {
        Serial.println("\n✅ WiFi reconnected successfully!");
        Serial.println("📍 IP: " + WiFi.localIP().toString());
        Serial.println("📶 RSSI: " + String(WiFi.RSSI()) + " dBm");
        quickReconnectAttempts = 0;  // 重置计数
    } else {
        Serial.println("\n❌ Quick reconnect failed");

        // 如果快速重连多次失败，使用完整重连
        if (quickReconnectAttempts >= 3) {
            Serial.println("🔄 Switching to full reconnect mode");
            connectToWiFi();  // 使用完整的连接流程
            quickReconnectAttempts = 0;
        }
    }
}

// NTP时间同步 (根据用户选择的服务器)
void syncNTPTime() {
    Serial.println("Syncing NTP time...");
    vfd.displayText("SYNC TIME...");

    // 根据用户选择配置NTP服务器
    const char* ntp1, *ntp2, *ntp3;

    switch (config.fontStyle) {  // 复用fontStyle字段
        case 1:  // 阿里云
            ntp1 = "ntp.aliyun.com";
            ntp2 = "ntp1.aliyun.com";
            ntp3 = "ntp2.aliyun.com";
            break;
        case 2:  // 腾讯云
            ntp1 = "ntp.tencent.com";
            ntp2 = "ntp1.tencent.com";
            ntp3 = "ntp2.tencent.com";
            break;
        case 3:  // 国际
            ntp1 = "pool.ntp.org";
            ntp2 = "time.nist.gov";
            ntp3 = "time.google.com";
            break;
        default:  // 自动选择
            ntp1 = "ntp.aliyun.com";
            ntp2 = "pool.ntp.org";
            ntp3 = "time.nist.gov";
            break;
    }

    // 设置时区 - 修正ESP32时区格式（GMT符号相反）
    // ESP32中：GMT-8表示东八区，GMT+8表示西八区
    String timezoneStr;
    if (config.timezone >= 0) {
        timezoneStr = "GMT-" + String(config.timezone);  // 东时区用负号
    } else {
        timezoneStr = "GMT+" + String(-config.timezone); // 西时区用正号
    }

    Serial.println("Setting timezone: " + timezoneStr + " (User selected: GMT" +
                   (config.timezone >= 0 ? "+" : "") + String(config.timezone) + ")");

    // 使用configTime设置时区和NTP服务器
    configTime(config.timezone * 3600, 0, ntp1, ntp2, ntp3);

    // 额外设置环境变量确保时区正确
    setenv("TZ", timezoneStr.c_str(), 1);
    tzset();

    int attempts = 0;
    while (time(nullptr) < 100000 && attempts < 20) {
        delay(500);
        Serial.print(".");
        attempts++;
    }

    if (time(nullptr) > 100000) {
        Serial.println("\nNTP time synced!");
        vfd.displayText("TIME SYNCED");
        delay(1000);
    } else {
        Serial.println("\nNTP sync failed!");
        vfd.displayText("SYNC FAILED");
        delay(2000);
    }
}

// 显示时间 (断网也继续显示基于RTC的时间)
// 字体设计仅对时间部分生效，日期部分始终使用默认圆形字体
void displayFullTime() {
    time_t now = time(nullptr);
    struct tm* timeinfo = localtime(&now);

    String currentTimeStr;

    if (timeinfo->tm_year < 100) {
        currentTimeStr = "   NO TIME SYNC   ";  // 居中显示
        // 只在时间变化时才更新VFD，避免频闪
        if (currentTimeStr != lastDisplayedTime) {
            vfd.displayText(currentTimeStr.c_str());  // 使用简单显示
            lastDisplayedTime = currentTimeStr;
            Serial.println("Time updated: " + currentTimeStr);
        }
    } else if (config.displayMode == 0) {
        // 模式1: 完整模式 - 混合字体显示
        // 时间部分使用选择的字体，日期部分使用默认字体
        char timePart[9];   // "HH:MM:SS"
        char datePart[6];   // "MM/DD"

        snprintf(timePart, sizeof(timePart), "%02d:%02d:%02d",
                timeinfo->tm_hour, timeinfo->tm_min, timeinfo->tm_sec);
        snprintf(datePart, sizeof(datePart), "%02d/%02d",
                timeinfo->tm_mon + 1, timeinfo->tm_mday);

        String newTimeStr = String(timePart) + " " + String(datePart);

        // 只在时间变化时才更新VFD，避免频闪
        if (newTimeStr != lastDisplayedTime) {
            displayMixedTimeAndDate(timePart, datePart);
            lastDisplayedTime = newTimeStr;
            Serial.println("Mixed display: " + String(timePart) + " (Font " + String(currentFontType) + ") + " + String(datePart) + " (Normal)");
        }
    } else {
        // 模式2: 仅时间模式 HH:MM:SS (居中显示)
        char timeStr[17];
        snprintf(timeStr, sizeof(timeStr), "   %02d:%02d:%02d   ",
                timeinfo->tm_hour, timeinfo->tm_min, timeinfo->tm_sec);
        currentTimeStr = String(timeStr);

        // 只在时间变化时才更新VFD，避免频闪
        if (currentTimeStr != lastDisplayedTime) {
            displayTimeWithFont(currentTimeStr.c_str());  // 时间使用选择的字体
            lastDisplayedTime = currentTimeStr;
            Serial.println("Time only: " + currentTimeStr + " (Font " + String(currentFontType) + ")");
        }
    }
}

// === 按钮控制功能 ===

// 初始化按钮 - 实际硬件配置
void initButtons() {
    Serial.println("=== Initializing Hardware Buttons ===");

    // 设置按钮引脚为输入上拉
    pinMode(BUTTON_MODE_PIN, INPUT_PULLUP);
    pinMode(BUTTON_IP_PIN, INPUT_PULLUP);
    pinMode(BUTTON_BRIGHTNESS_PIN, INPUT_PULLUP);

    delay(100);  // 等待引脚稳定

    // 读取初始状态
    lastModeButtonState = digitalRead(BUTTON_MODE_PIN);
    lastIpButtonState = digitalRead(BUTTON_IP_PIN);
    lastBrightnessButtonState = digitalRead(BUTTON_BRIGHTNESS_PIN);

    Serial.println("Hardware button configuration:");
    Serial.println("🔘 GPIO9 (Button1/Mode): " + String(lastModeButtonState) + " (should be 1)");
    Serial.println("🔲 GPIO2 (Button2/IP): " + String(lastIpButtonState) + " (should be 1)");
    Serial.println("🔺 GPIO10 (Button3/Font): " + String(lastBrightnessButtonState) + " (should be 1)");

    if (lastModeButtonState == HIGH && lastIpButtonState == HIGH && lastBrightnessButtonState == HIGH) {
        Serial.println("✅ All buttons initialized correctly!");
    } else {
        Serial.println("⚠️ Button hardware check:");
        if (lastModeButtonState != HIGH) Serial.println("  - GPIO9 (Button1) may have connection issue");
        if (lastIpButtonState != HIGH) Serial.println("  - GPIO2 (Button2) may have connection issue");
        if (lastBrightnessButtonState != HIGH) Serial.println("  - GPIO10 (Button3) may have connection issue");
    }

    Serial.println("Ready to detect button presses...");
}

// 检查按钮状态 - 统一简化逻辑
void checkButtons() {
    // 读取当前按钮状态
    bool modeButton = digitalRead(BUTTON_MODE_PIN);      // GPIO9 按钮1
    bool ipButton = digitalRead(BUTTON_IP_PIN);          // GPIO2 按钮2
    bool brightnessButton = digitalRead(BUTTON_BRIGHTNESS_PIN); // GPIO10 按钮3

    // 调试输出 - 每5秒打印一次状态
    if (millis() - lastButtonStatusPrint > 5000) {
        lastButtonStatusPrint = millis();
        Serial.println("🔘GPIO9=" + String(modeButton) + " 🔲GPIO2=" + String(ipButton) + " 🔺GPIO10=" + String(brightnessButton));
    }

    // 统一的按钮检测逻辑 - 检测下降沿（按下瞬间）

    // 按钮1 (GPIO9): 显示模式切换
    if (lastModeButtonState == HIGH && modeButton == LOW) {
        Serial.println("Button1 pressed - switching display mode");
        switchDisplayMode();
        delay(50); // 减少防抖延时，提高灵敏度
    }

    // 按钮2 (GPIO2): 字体切换
    if (lastIpButtonState == HIGH && ipButton == LOW) {
        Serial.println("Button2 pressed - switching font");
        switchFontType();
        delay(50); // 减少防抖延时，提高灵敏度
    }

    // 按钮3 (GPIO10): 亮度调节 (短按) / IP显示 (长按1秒)
    static unsigned long button3PressStart = 0;
    static bool button3LongHandled = false;
    static bool button3ShortHandled = false;

    if (brightnessButton == LOW) {
        if (lastBrightnessButtonState == HIGH) {
            // 刚按下
            button3PressStart = millis();
            button3LongHandled = false;
            button3ShortHandled = false;
        } else if (!button3LongHandled && (millis() - button3PressStart >= 1000)) {
            // 长按1秒 - 显示IP
            Serial.println("Button3 long press - showing IP");
            showIPAddress();
            button3LongHandled = true;
        }
    } else if (lastBrightnessButtonState == LOW && brightnessButton == HIGH) {
        // 按钮释放
        unsigned long pressDuration = millis() - button3PressStart;
        if (pressDuration < 1000 && !button3LongHandled) {
            // 短按 - 亮度调节
            Serial.println("Button3 short press - adjusting brightness");
            adjustBrightnessLevel();
            delay(50); // 减少防抖延时，提高灵敏度
        }
    }

    // 更新按钮状态
    lastModeButtonState = modeButton;
    lastIpButtonState = ipButton;
    lastBrightnessButtonState = brightnessButton;
}

// 亮度等级调节 (1-4等级循环，差异更明显)
void adjustBrightnessLevel() {
    // 亮度等级：更大差异 10%, 40%, 70%, 100%
    int brightnessLevels[] = {10, 40, 70, 100};

    // 根据当前亮度找到对应等级
    int currentLevel = 2; // 默认70%
    for (int i = 0; i < 4; i++) {
        if (config.brightness == brightnessLevels[i]) {
            currentLevel = i;
            break;
        }
    }

    // 切换到下一个等级
    currentLevel = (currentLevel + 1) % 4;
    int newBrightness = brightnessLevels[currentLevel];

    // 立即更新VFD亮度
    vfd.setBrightness(newBrightness);

    // 保存到配置并立即写入EEPROM
    config.brightness = newBrightness;
    saveConfig();

    // 只在Serial输出，不显示文案
    String levelNames[] = {"LOW", "MID", "HIGH", "MAX"};
    Serial.println("Brightness: " + levelNames[currentLevel] + " (" + String(newBrightness) + "%) - Level " + String(currentLevel + 1) + "/4");
}

// 切换显示模式 (只在时间模式间切换)
void switchDisplayMode() {
    config.displayMode = (config.displayMode + 1) % 2;  // 0, 1 循环
    saveConfig();

    Serial.println("Display mode switched to: " + String(config.displayMode));

    // 强制刷新时间显示
    lastDisplayedTime = "";
}

// 显示IP地址 - 优化显示
void showIPAddress() {
    String ipText;

    if (WiFi.status() == WL_CONNECTED) {
        ipText = "IP:" + WiFi.localIP().toString();
        Serial.println("Current IP: " + WiFi.localIP().toString());
    } else if (configMode) {
        ipText = "AP:***********";
        Serial.println("Access Point IP: ***********");
    } else {
        ipText = "NO NETWORK";
        Serial.println("No network connection");
    }

    // 格式化显示
    if (ipText.length() > 16) {
        ipText = ipText.substring(0, 16);
    }
    while (ipText.length() < 16) {
        ipText += " ";
    }

    vfd.displayText(ipText.c_str());
    delay(3000);
}

// 调试模式 - 隐藏功能
void enterDebugMode() {
    debugMode = !debugMode;
    Serial.println("Debug mode: " + String(debugMode ? "ON" : "OFF"));

    if (debugMode) {
        vfd.displayText("DEBUG MODE ON");
        delay(2000);

        // 显示系统信息
        String ramInfo = "RAM:" + String(ESP.getFreeHeap());
        vfd.displayText(ramInfo.c_str());
        delay(2000);
        String flashInfo = "FLASH:" + String(ESP.getFlashChipSize());
        vfd.displayText(flashInfo.c_str());
        delay(2000);
        String freqInfo = "FREQ:" + String(ESP.getCpuFreqMHz()) + "MHz";
        vfd.displayText(freqInfo.c_str());
        delay(2000);
    } else {
        vfd.displayText("DEBUG MODE OFF");
        delay(1000);
    }
}



// 切换字体类型 (严格按照1→2→3顺序)
void switchFontType() {
    // 当前字体: 0=默认, 1=方形, 2=粗体
    // 切换顺序: 0→1→2→0 (对应 默认→方形→粗体→默认)
    config.fontEffect = (config.fontEffect + 1) % 3;  // 0, 1, 2 循环
    currentFontType = config.fontEffect;
    saveConfig();

    String fontNames[] = {"DEFAULT", "SQUARE", "BOLD"};
    String fontNumbers[] = {"1", "2", "3"};
    Serial.println("Font switched to: " + fontNumbers[currentFontType] + " - " + fontNames[currentFontType]);

    // 强制刷新时间显示
    lastDisplayedTime = "";
}



// 根据字体类型显示时间 (支持3种字体风格)
void displayTimeWithFont(const char* timeStr) {
    vfd.displayTimeWithFontType(timeStr, currentFontType);
}

// 混合字体显示：时间部分使用选择字体，日期部分使用默认字体
// 日期向左移动一位，右侧留出2个空白位置，减少闪烁
void displayMixedTimeAndDate(const char* timePart, const char* datePart) {
    Serial.println("🎨 Stable mixed display - Time: '" + String(timePart) + "' (Font " + String(currentFontType) + "), Date: '" + String(datePart) + "' (Normal)");

    // 新布局：HH:MM:SS MM/DD  (14位，右侧留2位空白)
    // 位置：   0123456789012345
    //         HH:MM:SS MM/DD__
    //         01234567 890123 45
    //         时间部分  日期部分 空白

    // 1. 显示时间部分（使用选择的字体）
    vfd.displayTimeWithFontType(timePart, currentFontType);

    // 2. 用默认字体显示日期部分（位置8-13：" MM/DD"）
    int dateStartPos = 8;  // 从位置8开始（紧接着时间部分）
    String dateWithSpace = " " + String(datePart);  // 包含前面的空格，共6个字符

    // 使用writeString直接写入日期部分，避免全屏刷新
    vfd.writeString(dateStartPos, dateWithSpace.c_str());

    // 3. 清空右侧2位（位置14-15），确保没有残留字符
    vfd.writeString(14, "  ");  // 两个空格，从位置14开始

    Serial.println("✅ Stable mixed display complete - Layout: '" + String(timePart) + dateWithSpace + "  ' (Time font: " + String(currentFontType) + ", Date: Normal, Right: 2 spaces)");
}






