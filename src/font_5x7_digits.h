#ifndef FONT_5X7_DIGITS_H
#define FONT_5X7_DIGITS_H

#include <Arduino.h>

// 5x7 点阵数字字体 (0-9)
// 每列用 uint8_t 表示，最低7位有效 (bit0=顶部, bit6=底部)
// 格式: { col0, col1, col2, col3, col4 }

// ========== Normal 风格 ==========
const uint8_t digitFont_Normal[10][5] = {
    // 0: ▓▓▓▓▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓▓▓▓▓
    { 0x3E, 0x41, 0x41, 0x41, 0x3E },

    // 1:   ▓
    //      ▓
    //      ▓
    //      ▓
    //      ▓
    //      ▓
    //      ▓
    { 0x00, 0x42, 0x7F, 0x40, 0x00 },

    // 2: ▓▓▓▓▓
    //        ▓
    //        ▓
    //    ▓▓▓▓▓
    //    ▓
    //    ▓
    //    ▓▓▓▓▓
    { 0x42, 0x61, 0x51, 0x49, 0x46 },

    // 3: ▓▓▓▓▓
    //        ▓
    //        ▓
    //    ▓▓▓▓▓
    //        ▓
    //        ▓
    //    ▓▓▓▓▓
    { 0x21, 0x41, 0x45, 0x4B, 0x31 },

    // 4: ▓   ▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓▓▓▓▓
    //        ▓
    //        ▓
    //        ▓
    { 0x18, 0x14, 0x12, 0x7F, 0x10 },

    // 5: ▓▓▓▓▓
    //    ▓
    //    ▓
    //    ▓▓▓▓▓
    //        ▓
    //        ▓
    //    ▓▓▓▓▓
    { 0x27, 0x45, 0x45, 0x45, 0x39 },

    // 6: ▓▓▓▓▓
    //    ▓
    //    ▓
    //    ▓▓▓▓▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓▓▓▓▓
    { 0x3C, 0x4A, 0x49, 0x49, 0x30 },

    // 7: ▓▓▓▓▓
    //        ▓
    //        ▓
    //        ▓
    //        ▓
    //        ▓
    //        ▓
    { 0x01, 0x71, 0x09, 0x05, 0x03 },

    // 8: ▓▓▓▓▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓▓▓▓▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓▓▓▓▓
    { 0x36, 0x49, 0x49, 0x49, 0x36 },

    // 9: ▓▓▓▓▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓▓▓▓▓
    //        ▓
    //        ▓
    //    ▓▓▓▓▓
    { 0x06, 0x49, 0x49, 0x29, 0x1E }
};





// ========== Digital Square 风格 ==========
// 格式：6字节/字符 [列1, 列2, 列3, 列4, 列5, 保留] (基于C51方案)
const uint8_t digitFont_Square[10][7] = {
    // 0: 方形数字0 (7行×5列)
    { 0b11111, 0b10001, 0b10001, 0b10001, 0b10001, 0b10001, 0b11111 },

    // 1: 方形数字1 - 纯粹的光棍1（只有中间列全亮）
    { 0b00100, 0b00100, 0b00100, 0b00100, 0b00100, 0b00100, 0b00100 },

    // 2: 方形数字2
    { 0b11111, 0b00001, 0b00001, 0b11111, 0b10000, 0b10000, 0b11111 },

    // 3: 方形数字3
    { 0b11111, 0b00001, 0b00001, 0b01111, 0b00001, 0b00001, 0b11111 },

    // 4: 方形数字4
    { 0b10001, 0b10001, 0b10001, 0b11111, 0b00001, 0b00001, 0b00001 },

    // 5: 方形数字5
    { 0b11111, 0b10000, 0b10000, 0b11111, 0b00001, 0b00001, 0b11111 },

    // 6: 方形数字6
    { 0b11111, 0b10000, 0b10000, 0b11111, 0b10001, 0b10001, 0b11111 },

    // 7: 方形数字7
    { 0b11111, 0b00001, 0b00001, 0b00001, 0b00001, 0b00001, 0b00001 },

    // 8: 方形数字8
    { 0b11111, 0b10001, 0b10001, 0b11111, 0b10001, 0b10001, 0b11111 },

    // 9: 方形数字9
    { 0b11111, 0b10001, 0b10001, 0b11111, 0b00001, 0b00001, 0b11111 }
};

// ========== Bold/Thick 粗体风格 ==========
// 格式：7行×5列，上下横排单排，左右竖排双排（方形+粗体）
const uint8_t digitFont_Bold[10][7] = {
    // 0: 粗体数字0 - 上下单排，左右双排
    //    █████
    //    ██ ██
    //    ██ ██
    //    ██ ██
    //    ██ ██
    //    ██ ██
    //    █████
    { 0b11111, 0b11011, 0b11011, 0b11011, 0b11011, 0b11011, 0b11111 },

    // 1: 粗体数字1 - 修正版（第1列：只有第2,3行亮；第2,3列：全亮）
    //     ██    <- 第1行：第2,3列亮
    //    ███    <- 第2行：第1,2,3列亮
    //    ███    <- 第3行：第1,2,3列亮
    //     ██    <- 第4行：第2,3列亮
    //     ██    <- 第5行：第2,3列亮
    //     ██    <- 第6行：第2,3列亮
    //     ██    <- 第7行：第2,3列亮
    { 0b01100, 0b11100, 0b11100, 0b01100, 0b01100, 0b01100, 0b01100 },

    // 2: 粗体数字2 - 上下单排，左右双排
    //    █████
    //       ██
    //       ██
    //    █████
    //    ██
    //    ██
    //    █████
    { 0b11111, 0b00011, 0b00011, 0b11111, 0b11000, 0b11000, 0b11111 },

    // 3: 粗体数字3 - 上下单排，右边双排
    //    █████
    //       ██
    //       ██
    //    █████
    //       ██
    //       ██
    //    █████
    { 0b11111, 0b00011, 0b00011, 0b11111, 0b00011, 0b00011, 0b11111 },

    // 4: 粗体数字4 - 左右双排
    //    ██ ██
    //    ██ ██
    //    ██ ██
    //    █████
    //       ██
    //       ██
    //       ██
    { 0b11011, 0b11011, 0b11011, 0b11111, 0b00011, 0b00011, 0b00011 },

    // 5: 粗体数字5 - 上下单排，左右双排
    //    █████
    //    ██
    //    ██
    //    █████
    //       ██
    //       ██
    //    █████
    { 0b11111, 0b11000, 0b11000, 0b11111, 0b00011, 0b00011, 0b11111 },

    // 6: 粗体数字6 - 上下单排，左右双排
    //    █████
    //    ██
    //    ██
    //    █████
    //    ██ ██
    //    ██ ██
    //    █████
    { 0b11111, 0b11000, 0b11000, 0b11111, 0b11011, 0b11011, 0b11111 },

    // 7: 粗体数字7 - 上排单排，右边双排
    //    █████
    //       ██
    //      ██
    //      ██
    //     ██
    //     ██
    //     ██
    { 0b11111, 0b00011, 0b00110, 0b00110, 0b01100, 0b01100, 0b01100 },

    // 8: 粗体数字8 - 上下单排，左右双排
    //    █████
    //    ██ ██
    //    ██ ██
    //    █████
    //    ██ ██
    //    ██ ██
    //    █████
    { 0b11111, 0b11011, 0b11011, 0b11111, 0b11011, 0b11011, 0b11111 },

    // 9: 粗体数字9 - 上下单排，左右双排
    //    █████
    //    ██ ██
    //    ██ ██
    //    █████
    //       ██
    //       ██
    //    █████
    { 0b11111, 0b11011, 0b11011, 0b11111, 0b00011, 0b00011, 0b11111 }
};

// 外部声明，供其他文件使用
extern const uint8_t digitFont_Normal[10][5];   // 默认圆形字体
extern const uint8_t digitFont_Square[10][7];   // 方形字体
extern const uint8_t digitFont_Bold[10][7];     // 粗体字体（方形+粗体）

#endif // FONT_5X7_DIGITS_H
