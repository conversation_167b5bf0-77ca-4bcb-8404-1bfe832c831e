// Arduino/ESP32 字体使用示例
#include "font_5x7_digits.h"

// 字体风格枚举
enum FontStyle {
    FONT_NORMAL = 0,
    FONT_BOLD = 1,
    FONT_ROUNDED = 2
};

// 获取指定风格的字体数据
const uint8_t* getFontData(FontStyle style) {
    switch(style) {
        case FONT_NORMAL:  return (const uint8_t*)digitFont_Normal;
        case FONT_BOLD:    return (const uint8_t*)digitFont_Bold;
        case FONT_ROUNDED: return (const uint8_t*)digitFont_Rounded;
        default:           return (const uint8_t*)digitFont_Normal;
    }
}

// 获取单个数字的字体数据
void getDigitFont(uint8_t digit, FontStyle style, uint8_t* buffer) {
    if (digit > 9) return;
    
    const uint8_t* fontData = getFontData(style);
    const uint8_t* digitData = fontData + (digit * 5);
    
    for (int i = 0; i < 5; i++) {
        buffer[i] = digitData[i];
    }
}

// 显示数字到VFD或LED矩阵 (示例函数)
void displayDigit(uint8_t digit, FontStyle style, int x_offset = 0) {
    uint8_t fontBuffer[5];
    getDigitFont(digit, style, fontBuffer);
    
    // 这里是你的显示逻辑
    // 例如：发送到VFD控制器、LED矩阵等
    for (int col = 0; col < 5; col++) {
        for (int row = 0; row < 7; row++) {
            bool pixel = fontBuffer[col] & (1 << row);
            // setPixel(x_offset + col, row, pixel);
        }
    }
}

// 显示多位数字
void displayNumber(int number, FontStyle style, int x_offset = 0) {
    String numStr = String(number);
    int digitWidth = 6; // 5像素宽度 + 1像素间隔
    
    for (int i = 0; i < numStr.length(); i++) {
        uint8_t digit = numStr.charAt(i) - '0';
        displayDigit(digit, style, x_offset + (i * digitWidth));
    }
}

// 时间显示示例 (HH:MM格式)
void displayTime(int hours, int minutes, FontStyle style) {
    // 显示小时
    displayDigit(hours / 10, style, 0);   // 十位
    displayDigit(hours % 10, style, 6);   // 个位
    
    // 显示冒号 (可以用自定义图案)
    // displayColon(12);
    
    // 显示分钟
    displayDigit(minutes / 10, style, 18); // 十位
    displayDigit(minutes % 10, style, 24); // 个位
}

// 字体切换示例
void cycleFontStyle() {
    static FontStyle currentStyle = FONT_NORMAL;
    static unsigned long lastChange = 0;
    
    if (millis() - lastChange > 3000) { // 每3秒切换
        currentStyle = (FontStyle)((currentStyle + 1) % 3);
        lastChange = millis();
        
        // 重新显示当前时间
        // displayTime(getCurrentHour(), getCurrentMinute(), currentStyle);
    }
}

// 使用示例
void setup() {
    // 初始化显示器
    // initDisplay();
    
    // 显示测试数字
    displayNumber(1234, FONT_NORMAL, 0);
    delay(2000);
    
    displayNumber(1234, FONT_BOLD, 0);
    delay(2000);
    
    displayNumber(1234, FONT_ROUNDED, 0);
    delay(2000);
}

void loop() {
    // 获取当前时间
    // int hours = getCurrentHour();
    // int minutes = getCurrentMinute();
    
    // 显示时间
    // displayTime(hours, minutes, FONT_NORMAL);
    
    // 字体风格循环
    // cycleFontStyle();
    
    delay(1000);
}
