# ESP32-C3 PT6302 VFD 使用说明

## 快速开始

### 1. 硬件连接
确保你的硬件连接如下：
- ESP32-C3 GPIO3 → VFD CLK
- ESP32-C3 GPIO5 → VFD DIN  
- ESP32-C3 GPIO8 → VFD CS
- 电源和地线正确连接

### 2. 编译和烧录
```bash
# 编译项目
make build

# 或者编译并上传
make flash
```

### 3. 基本使用

```cpp
#include "VFD_Driver.h"

VFD_Driver vfd;

void setup() {
    Serial.begin(115200);
    
    // 初始化VFD
    if (vfd.begin()) {
        Serial.println("VFD初始化成功!");
        
        // 设置亮度
        vfd.setBrightness(75);  // 75%亮度
        
        // 显示文本
        vfd.displayText("HELLO ESP32-C3!");
        
    } else {
        Serial.println("VFD初始化失败!");
    }
}

void loop() {
    // 你的代码
}
```

## 常用功能

### 显示文本
```cpp
vfd.displayText("HELLO WORLD");
```

### 显示数字
```cpp
vfd.displayNumber(12345);
```

### 单个字符控制
```cpp
// 在位置0显示字符'A'
vfd.writeOneChar(0, 'A');

// 在位置5开始显示字符串
vfd.writeString(5, "TEST");
```

### 亮度控制
```cpp
vfd.setBrightness(50);  // 设置为50%亮度
```

### 清屏
```cpp
vfd.clear();
```

## 基于你的原始代码

这个驱动基于你的原始ESP32C3.ino代码，保持了相同的硬件接口和PT6302控制逻辑：

- `writeOneChar()` 对应你的 `S1201_WriteOneChar()`
- `writeString()` 对应你的 `S1201_WriteStr()`  
- `showDisplay()` 对应你的 `S1201_show()`
- `sendByte()` 对应你的 `write_6302()`

## 故障排除

### VFD不显示
1. 检查硬件连接
2. 检查电源电压
3. 确认引脚定义正确

### 显示乱码
1. 检查字符编码
2. 确认PT6302初始化正确

### 编译错误
1. 确保PlatformIO已安装
2. 检查platformio.ini配置

## 扩展功能

如果需要添加更多功能（如RTC、按键等），可以参考你的原始代码进行扩展。

## 技术支持

如有问题，请检查：
1. 硬件连接是否正确
2. 代码是否正确编译
3. 串口输出的调试信息
