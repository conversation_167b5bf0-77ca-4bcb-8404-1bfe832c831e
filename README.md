# ESP32-C3 PT6302 真空荧光显示屏驱动

这是一个用于ESP32-C3芯片驱动16位PT6302真空荧光显示屏(VFD)的项目。基于你的现有硬件配置优化。

## 硬件要求

- ESP32-C3开发板
- 16位PT6302 VFD显示屏
- DS3231 RTC时钟芯片 (可选)
- 按键和蜂鸣器 (可选)
- 连接线

## 引脚连接 (基于你的现有硬件)

| ESP32-C3 引脚 | VFD 信号 | 说明 |
|---------------|----------|------|
| GPIO3 | CLK | 时钟信号 |
| GPIO5 | DIN | 数据信号 |
| GPIO8 | CS | 片选信号 |
| GPIO18 | SDA | I2C数据 (RTC) |
| GPIO19 | SCL | I2C时钟 (RTC) |
| GPIO9 | BUTTON1 | 按键1 |
| GPIO2 | BUTTON2 | 按键2 |
| GPIO10 | BUTTON3 | 按键3 |
| GPIO4 | BEEP | 蜂鸣器 |
| GND | GND | 地线 |
| 3.3V/5V | VCC | 电源 |

**注意**: 这些引脚配置基于你的现有代码，已经过验证可用。

## 功能特性

- ✅ PT6302芯片驱动
- ✅ 基本显示控制
- ✅ 文本显示
- ✅ 数字显示
- ✅ 亮度控制
- ✅ 清屏功能
- ✅ 单个字符控制
- ✅ 基于你的原始代码优化

## 编译和烧录

1. 安装PlatformIO
2. 克隆或下载此项目
3. 根据你的硬件调整引脚定义 (`src/VFD_Driver.h`)
4. 编译项目:
   ```bash
   pio run
   ```
5. 烧录到ESP32-C3:
   ```bash
   pio run --target upload
   ```
6. 查看串口输出:
   ```bash
   pio device monitor
   ```

## 使用示例

```cpp
#include "VFD_Driver.h"

VFD_Driver vfd;

void setup() {
    vfd.begin();
    vfd.setBrightness(75);
    vfd.displayText("HELLO WORLD!");
}

void loop() {
    static int counter = 0;
    vfd.displayNumber(counter++);
    delay(1000);
}
```

## API 参考

### 初始化
- `bool begin()` - 初始化PT6302 VFD驱动

### 显示控制
- `void clear()` - 清空显示
- `void setBrightness(uint8_t level)` - 设置亮度 (0-100)

### 文本显示
- `void displayText(const char* text)` - 显示文本
- `void displayNumber(long number)` - 显示数字
- `void writeOneChar(uint8_t position, char character)` - 单字符显示
- `void writeString(uint8_t position, const char* str)` - 字符串显示
- `void showDisplay()` - 刷新显示

## 注意事项

1. 请确保VFD的电源电压与ESP32-C3兼容
2. 根据实际VFD控制器调整数据格式和时序
3. 某些VFD可能需要特定的初始化序列
4. 建议先用示波器检查信号时序

## 故障排除

- **显示不亮**: 检查电源连接和引脚定义
- **显示乱码**: 检查数据格式和字符映射表
- **闪烁**: 检查时钟信号和选通时序
- **亮度问题**: 调整亮度控制方法

## 许可证

MIT License
