#include <iostream>
#include <iomanip>
#include "font_5x7_digits.h"

// 可视化显示字体点阵
void displayFont(const uint8_t font[10][5], const char* styleName) {
    std::cout << "\n========== " << styleName << " 风格 ==========\n";
    
    for (int digit = 0; digit < 10; digit++) {
        std::cout << "\n数字 " << digit << ":\n";
        
        // 显示二进制表示
        std::cout << "代码: { ";
        for (int col = 0; col < 5; col++) {
            std::cout << "0x" << std::hex << std::uppercase << std::setfill('0') << std::setw(2) 
                      << (int)font[digit][col];
            if (col < 4) std::cout << ", ";
        }
        std::cout << " }\n";
        
        // 显示点阵图
        std::cout << "点阵:\n";
        for (int row = 0; row < 7; row++) {
            for (int col = 0; col < 5; col++) {
                if (font[digit][col] & (1 << row)) {
                    std::cout << "██";  // 实心方块
                } else {
                    std::cout << "  ";  // 空白
                }
            }
            std::cout << "\n";
        }
    }
}

// 显示所有数字在一行
void displayAllDigits(const uint8_t font[10][5], const char* styleName) {
    std::cout << "\n" << styleName << " - 所有数字预览:\n";
    
    // 显示数字标签
    for (int digit = 0; digit < 10; digit++) {
        std::cout << "  " << digit << "   ";
    }
    std::cout << "\n";
    
    // 显示点阵 (7行)
    for (int row = 0; row < 7; row++) {
        for (int digit = 0; digit < 10; digit++) {
            for (int col = 0; col < 5; col++) {
                if (font[digit][col] & (1 << row)) {
                    std::cout << "█";
                } else {
                    std::cout << " ";
                }
            }
            std::cout << " ";  // 数字间隔
        }
        std::cout << "\n";
    }
}

int main() {
    std::cout << "5×7 点阵数字字体测试\n";
    std::cout << "====================\n";
    
    // 显示所有风格的预览
    displayAllDigits(digitFont_Normal, "Normal");
    displayAllDigits(digitFont_Bold, "Bold");
    displayAllDigits(digitFont_Rounded, "Rounded");
    
    // 详细显示每个数字
    displayFont(digitFont_Normal, "Normal");
    displayFont(digitFont_Bold, "Bold");
    displayFont(digitFont_Rounded, "Rounded");
    
    return 0;
}
