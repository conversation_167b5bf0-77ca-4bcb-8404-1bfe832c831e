# API集成设置指南

## 🔑 获取API密钥

### 1. OpenWeatherMap API (天气数据)
1. 访问 https://openweathermap.org/api
2. 注册免费账户
3. 获取API密钥
4. 免费版本：每分钟60次调用，每月100万次调用

### 2. 其他免费API (无需密钥)
- **WorldTimeAPI**: 获取世界时间 (无需密钥)
- **CoinGecko**: 加密货币价格 (无需密钥)
- **JSONPlaceholder**: 测试数据 (无需密钥)

## ⚙️ 配置步骤

### 1. 修改WiFi设置
编辑 `src/config.h` 文件：
```cpp
#define WIFI_SSID "你的WiFi名称"
#define WIFI_PASSWORD "你的WiFi密码"
```

### 2. 设置API密钥
```cpp
#define OPENWEATHER_API_KEY "你的OpenWeatherMap API密钥"
```

### 3. 选择城市
```cpp
#define WEATHER_CITY "Beijing"  // 改为你的城市
```

## 📊 显示内容

程序会循环显示：
1. **天气信息**: "Sunny 25C" (天气状况和温度)
2. **当前时间**: "TIME:14:30:25" 
3. **比特币价格**: "BTC $45000"

每30秒更新一次数据。

## 🚀 编译和烧录

```bash
# 编译
pio run

# 烧录
pio run --target upload

# 查看串口输出
pio device monitor
```

## 🔧 故障排除

### WiFi连接失败
- 检查WiFi名称和密码
- 确保ESP32-C3在WiFi信号范围内
- 检查WiFi是否支持2.4GHz

### API调用失败
- 检查API密钥是否正确
- 确认网络连接正常
- 查看串口输出的错误信息

### VFD显示问题
- 检查硬件连接
- 确认VFD电源正常
- 调整显示亮度

## 📈 扩展功能

你可以添加更多API：

### 股票价格 (Alpha Vantage)
```cpp
String url = "https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol=AAPL&apikey=" + apiKey;
```

### 新闻标题 (NewsAPI)
```cpp
String url = "https://newsapi.org/v2/top-headlines?country=cn&apikey=" + apiKey;
```

### 汇率 (ExchangeRate-API)
```cpp
String url = "https://api.exchangerate-api.com/v4/latest/USD";
```

## 🎯 下一步

1. 获取OpenWeatherMap API密钥
2. 修改config.h中的WiFi和API设置
3. 重新编译和烧录
4. 享受实时数据显示！
