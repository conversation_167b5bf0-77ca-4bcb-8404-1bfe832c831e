# ESP32-C3 VFD Driver Makefile
# 简化PlatformIO命令的Makefile

.PHONY: all build upload monitor clean test help

# 默认目标
all: build

# 编译项目
build:
	@echo "正在编译ESP32-C3 VFD驱动..."
	pio run

# 编译并上传
upload:
	@echo "正在编译并上传到ESP32-C3..."
	pio run --target upload

# 串口监视器
monitor:
	@echo "启动串口监视器..."
	pio device monitor

# 编译、上传并监视
flash: upload monitor

# 清理编译文件
clean:
	@echo "清理编译文件..."
	pio run --target clean

# 运行测试
test:
	@echo "运行测试..."
	pio test

# 检查代码
check:
	@echo "检查代码..."
	pio check

# 更新库依赖
update:
	@echo "更新库依赖..."
	pio lib update

# 安装库依赖
install:
	@echo "安装库依赖..."
	pio lib install

# 显示设备信息
devices:
	@echo "显示连接的设备..."
	pio device list

# 格式化代码
format:
	@echo "格式化代码..."
	find src -name "*.cpp" -o -name "*.h" | xargs clang-format -i

# 显示帮助信息
help:
	@echo "ESP32-C3 VFD Driver 编译帮助"
	@echo ""
	@echo "可用命令:"
	@echo "  build    - 编译项目"
	@echo "  upload   - 编译并上传到ESP32-C3"
	@echo "  monitor  - 启动串口监视器"
	@echo "  flash    - 编译、上传并监视"
	@echo "  clean    - 清理编译文件"
	@echo "  test     - 运行测试"
	@echo "  check    - 检查代码"
	@echo "  update   - 更新库依赖"
	@echo "  install  - 安装库依赖"
	@echo "  devices  - 显示连接的设备"
	@echo "  format   - 格式化代码"
	@echo "  help     - 显示此帮助信息"
	@echo ""
	@echo "快速开始:"
	@echo "  make build   # 编译项目"
	@echo "  make flash   # 编译、上传并监视"
